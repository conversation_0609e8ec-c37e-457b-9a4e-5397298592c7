{"version": 3, "file": "ownership-rule.js", "sourceRoot": "", "sources": ["../../../../../src/gtd/clarify/decision-engine/rules/ownership-rule.ts"], "names": [], "mappings": ";AAAA,sBAAsB;;;AAItB,+CAIyB;AAEzB;;;GAGG;AACH,MAAa,aAAa;IACR,IAAI,GAAG,eAAe,CAAC;IACvB,QAAQ,GAAG,2BAAe,CAAC,SAAS,CAAC;IAErD,KAAK,CAAC,QAAQ,CAAC,OAAwB;QACrC,MAAM,IAAI,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;QAE/B,yBAAyB;QACzB,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QACvC,IAAI,QAAQ,CAAC,YAAY,KAAK,KAAK,EAAE,CAAC;YACpC,OAAO;gBACL,QAAQ,EAAE,IAAI,CAAC,IAAI;gBACnB,QAAQ,EAAE,EAAE;gBACZ,UAAU,EAAE,iCAAqB,CAAC,IAAI;gBACtC,SAAS,EAAE,CAAC,gBAAgB,CAAC;gBAC7B,cAAc,EAAE,IAAI;aACrB,CAAC;QACJ,CAAC;QAED,IAAI,QAAQ,CAAC,iBAAiB,KAAK,IAAI,EAAE,CAAC;YACxC,OAAO;gBACL,QAAQ,EAAE,IAAI,CAAC,IAAI;gBACnB,QAAQ,EAAE;oBACR,kBAAkB,EAAE,IAAI;iBACzB;gBACD,UAAU,EAAE,iCAAqB,CAAC,IAAI;gBACtC,SAAS,EAAE,CAAC,YAAY,CAAC;gBACzB,cAAc,EAAE,IAAI;aACrB,CAAC;QACJ,CAAC;QAED,SAAS;QACT,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAEzD,SAAS;QACT,MAAM,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAEnE,SAAS;QACT,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,kBAAkB,CAAC,CAAC;QAEpE,QAAQ;QACR,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QAEtD,gBAAgB;QAChB,IAAI,UAA8B,CAAC;QACnC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACxB,UAAU,GAAG,SAAS,CAAC;QACzB,CAAC;QAED,OAAO;YACL,QAAQ,EAAE,IAAI,CAAC,IAAI;YACnB,QAAQ,EAAE;gBACR,kBAAkB;gBAClB,UAAU,EAAE,UAAiB;aAC9B;YACD,UAAU;YACV,SAAS;YACT,cAAc,EAAE,kBAAkB,CAAC,gBAAgB;SACpD,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,WAAmB;QAC1C,MAAM,IAAI,GAAG,WAAW,CAAC,WAAW,EAAE,CAAC;QAEvC,MAAM,QAAQ,GAAsB;YAClC,uBAAuB,EAAE,KAAK;YAC9B,mBAAmB,EAAE,KAAK;YAC1B,iBAAiB,EAAE,KAAK;YACxB,kBAAkB,EAAE,EAAE;YACtB,cAAc,EAAE,EAAE;YAClB,YAAY,EAAE,EAAE;YAChB,cAAc,EAAE,IAAI;SACrB,CAAC;QAEF,UAAU;QACV,KAAK,MAAM,SAAS,IAAI,+BAAmB,CAAC,qBAAqB,EAAE,CAAC;YAClE,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC7B,QAAQ,CAAC,uBAAuB,GAAG,IAAI,CAAC;gBACxC,QAAQ,CAAC,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC9C,CAAC;QACH,CAAC;QAED,UAAU;QACV,KAAK,MAAM,MAAM,IAAI,+BAAmB,CAAC,iBAAiB,EAAE,CAAC;YAC3D,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC1B,QAAQ,CAAC,mBAAmB,GAAG,IAAI,CAAC;gBACpC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACvC,CAAC;QACH,CAAC;QAED,aAAa;QACb,MAAM,cAAc,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QACrD,KAAK,MAAM,IAAI,IAAI,cAAc,EAAE,CAAC;YAClC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBACxB,QAAQ,CAAC,iBAAiB,GAAG,IAAI,CAAC;gBAClC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnC,CAAC;QACH,CAAC;QAED,eAAe;QACf,QAAQ,CAAC,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;QAE3D,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,IAAY;QACxC,cAAc;QACd,MAAM,QAAQ,GAAG;YACf,8BAA8B;YAC9B,8BAA8B;YAC9B,8BAA8B;YAC9B,yBAAyB;YACzB,yBAAyB;YACzB,yBAAyB;SAC1B,CAAC;QAEF,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAClC,IAAI,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;gBACtB,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;gBAC/B,cAAc;gBACd,IAAI,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;oBACrD,OAAO,MAAM,CAAC;gBAChB,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,QAA2B,EAAE,IAAS;QAC/D,SAAS;QACT,IAAI,QAAQ,CAAC,uBAAuB,IAAI,CAAC,QAAQ,CAAC,mBAAmB,IAAI,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;YAClG,OAAO,KAAK,CAAC;QACf,CAAC;QAED,aAAa;QACb,IAAI,QAAQ,CAAC,iBAAiB,EAAE,CAAC;YAC/B,OAAO,IAAI,CAAC;QACd,CAAC;QAED,eAAe;QACf,IAAI,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;YAChD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,iBAAiB;QACjB,IAAI,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;YAC/C,OAAO,IAAI,CAAC;QACd,CAAC;QAED,iBAAiB;QACjB,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,WAAmB;QAC9C,MAAM,IAAI,GAAG,WAAW,CAAC,WAAW,EAAE,CAAC;QAEvC,MAAM,eAAe,GAAG;YACtB,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI;YACnC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;YAChC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;SAC7B,CAAC;QAEF,OAAO,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3D,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,WAAmB;QAC7C,MAAM,IAAI,GAAG,WAAW,CAAC,WAAW,EAAE,CAAC;QAEvC,MAAM,aAAa,GAAG;YACpB,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;YAClC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;YAClC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;SAC7B,CAAC;QAEF,OAAO,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,QAA2B,EAAE,kBAA2B;QAC7E,MAAM,SAAS,GAAa,EAAE,CAAC;QAE/B,IAAI,kBAAkB,EAAE,CAAC;YACvB,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAE7B,IAAI,QAAQ,CAAC,iBAAiB,EAAE,CAAC;gBAC/B,SAAS,CAAC,IAAI,CAAC,eAAe,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACpE,CAAC;YAED,IAAI,CAAC,QAAQ,CAAC,uBAAuB,EAAE,CAAC;gBACtC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC9B,CAAC;QACH,CAAC;aAAM,CAAC;YACN,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAE7B,IAAI,QAAQ,CAAC,uBAAuB,EAAE,CAAC;gBACrC,SAAS,CAAC,IAAI,CAAC,aAAa,QAAQ,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACxE,CAAC;YAED,IAAI,QAAQ,CAAC,mBAAmB,EAAE,CAAC;gBACjC,SAAS,CAAC,IAAI,CAAC,aAAa,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACpE,CAAC;YAED,IAAI,QAAQ,CAAC,cAAc,EAAE,CAAC;gBAC5B,SAAS,CAAC,IAAI,CAAC,YAAY,QAAQ,CAAC,cAAc,EAAE,CAAC,CAAC;YACxD,CAAC;YAED,SAAS,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACpC,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,QAA2B;QACrD,IAAI,UAAU,GAAG,iCAAqB,CAAC,MAAM,CAAC;QAE9C,kBAAkB;QAClB,IAAI,QAAQ,CAAC,uBAAuB,IAAI,QAAQ,CAAC,mBAAmB,EAAE,CAAC;YACrE,UAAU,IAAI,GAAG,CAAC;QACpB,CAAC;QAED,iBAAiB;QACjB,IAAI,QAAQ,CAAC,cAAc,EAAE,CAAC;YAC5B,UAAU,IAAI,GAAG,CAAC;QACpB,CAAC;QAED,oBAAoB;QACpB,IAAI,QAAQ,CAAC,iBAAiB,EAAE,CAAC;YAC/B,UAAU,IAAI,GAAG,CAAC;QACpB,CAAC;QAED,2BAA2B;QAC3B,IAAI,QAAQ,CAAC,uBAAuB,IAAI,QAAQ,CAAC,iBAAiB,EAAE,CAAC;YACnE,UAAU,IAAI,GAAG,CAAC;QACpB,CAAC;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,iCAAqB,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC,CAAC;IACxE,CAAC;CACF;AAnQD,sCAmQC"}