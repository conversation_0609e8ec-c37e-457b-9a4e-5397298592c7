"use strict";
// 日历规则 - 判断是否需要放入日历
Object.defineProperty(exports, "__esModule", { value: true });
exports.CalendarRule = void 0;
const constants_1 = require("../../constants");
/**
 * 日历规则
 * 判断任务是否有特定时间要求，需要放入日历
 * 只有在特定日期/时间必须执行的事项才放入日历
 */
class CalendarRule {
    name = 'CalendarRule';
    priority = constants_1.RULE_PRIORITIES.CALENDAR;
    async evaluate(context) {
        const item = context.getItem();
        // 只对单一行动应用日历规则
        const decision = context.getDecision();
        if (decision.isActionable === false ||
            decision.isMyResponsibility === false ||
            decision.isSingleAction === false) {
            return {
                ruleName: this.name,
                decision: {},
                confidence: constants_1.CONFIDENCE_THRESHOLDS.HIGH,
                reasoning: ['跳过日历规则：非单一行动或非自己负责'],
                shouldContinue: true
            };
        }
        // 分析时间特定性
        const analysis = this.analyzeTimeSpecificity(item);
        // 判断是否需要放入日历
        const hasSpecificTime = this.determineCalendarNeed(analysis, item);
        // 构建推理过程
        const reasoning = this.buildReasoning(analysis, hasSpecificTime);
        // 计算置信度
        const confidence = this.calculateConfidence(analysis);
        // 设置目标清单
        let targetList;
        if (hasSpecificTime) {
            targetList = 'calendar';
        }
        // 如果不需要放入日历，保持之前的决策（通常是next-actions）
        return {
            ruleName: this.name,
            decision: {
                hasSpecificTime,
                targetList: targetList
            },
            confidence,
            reasoning,
            shouldContinue: false // 这是最后一个规则
        };
    }
    /**
     * 分析时间特定性
     */
    analyzeTimeSpecificity(item) {
        const description = item.description.toLowerCase();
        const analysis = {
            hasTimeSpecificWords: false,
            hasLocationSpecificWords: false,
            hasDeadline: false,
            hasTimePatterns: false,
            timeSpecificKeywords: [],
            locationKeywords: [],
            extractedTimes: [],
            extractedDates: [],
            isRecurring: false
        };
        // 检查时间特定关键词
        for (const keyword of constants_1.CALENDAR_KEYWORDS.TIME_SPECIFIC) {
            if (description.includes(keyword)) {
                analysis.hasTimeSpecificWords = true;
                analysis.timeSpecificKeywords.push(keyword);
            }
        }
        // 检查地点特定关键词
        for (const keyword of constants_1.CALENDAR_KEYWORDS.LOCATION_SPECIFIC) {
            if (description.includes(keyword)) {
                analysis.hasLocationSpecificWords = true;
                analysis.locationKeywords.push(keyword);
            }
        }
        // 检查是否有截止日期
        analysis.hasDeadline = !!item.deadline;
        // 检查时间模式
        for (const pattern of constants_1.CALENDAR_KEYWORDS.TIME_PATTERNS) {
            const matches = description.match(pattern);
            if (matches) {
                analysis.hasTimePatterns = true;
                analysis.extractedTimes.push(...matches);
            }
        }
        // 检查是否为重复事件
        analysis.isRecurring = this.checkRecurring(description);
        return analysis;
    }
    /**
     * 检查是否为重复事件
     */
    checkRecurring(description) {
        const recurringIndicators = [
            '每天', '每周', '每月', '每年', '定期', '例会',
            '周会', '月会', '年会', '日常', '常规'
        ];
        return recurringIndicators.some(indicator => description.includes(indicator));
    }
    /**
     * 判断是否需要放入日历
     */
    determineCalendarNeed(analysis, item) {
        // 强日历指示器：会议、约会等
        if (analysis.hasTimeSpecificWords) {
            const strongCalendarWords = ['会议', '约会', '面试', '培训', '课程', '讲座'];
            if (strongCalendarWords.some(word => analysis.timeSpecificKeywords.some(keyword => keyword.includes(word)))) {
                return true;
            }
        }
        // 有具体时间模式
        if (analysis.hasTimePatterns && analysis.extractedTimes.length > 0) {
            return true;
        }
        // 重复事件
        if (analysis.isRecurring) {
            return true;
        }
        // 有截止日期且是时间敏感的任务
        if (analysis.hasDeadline && this.isTimeSensitive(item.description)) {
            return true;
        }
        // 地点特定且有时间要求
        if (analysis.hasLocationSpecificWords && analysis.hasTimePatterns) {
            return true;
        }
        return false;
    }
    /**
     * 检查是否为时间敏感任务
     */
    isTimeSensitive(description) {
        const text = description.toLowerCase();
        const timeSensitiveIndicators = [
            '截止', '到期', '最晚', '必须', '务必', '紧急',
            '立即', '马上', '今天', '明天', '本周'
        ];
        return timeSensitiveIndicators.some(indicator => text.includes(indicator));
    }
    /**
     * 构建推理过程
     */
    buildReasoning(analysis, hasSpecificTime) {
        const reasoning = [];
        if (hasSpecificTime) {
            reasoning.push('判断为需要放入日历的事项');
            reasoning.push('有特定的时间或地点要求');
            if (analysis.hasTimeSpecificWords) {
                reasoning.push(`检测到时间特定关键词: ${analysis.timeSpecificKeywords.join(', ')}`);
            }
            if (analysis.hasTimePatterns) {
                reasoning.push(`检测到时间模式: ${analysis.extractedTimes.join(', ')}`);
            }
            if (analysis.isRecurring) {
                reasoning.push('检测到重复事件指示器');
            }
            if (analysis.hasLocationSpecificWords) {
                reasoning.push(`检测到地点关键词: ${analysis.locationKeywords.join(', ')}`);
            }
            reasoning.push('建议设置具体的日期和时间');
        }
        else {
            reasoning.push('判断为执行清单事项');
            reasoning.push('没有特定的时间要求');
            if (analysis.hasDeadline) {
                reasoning.push('虽有截止日期，但可灵活安排执行时间');
            }
            reasoning.push('可以根据情境和可用时间灵活执行');
        }
        return reasoning;
    }
    /**
     * 计算置信度
     */
    calculateConfidence(analysis) {
        let confidence = constants_1.CONFIDENCE_THRESHOLDS.MEDIUM;
        // 有强时间特定指示器，提高置信度
        if (analysis.hasTimeSpecificWords && analysis.hasTimePatterns) {
            confidence += 0.3;
        }
        // 有重复事件指示器，提高置信度
        if (analysis.isRecurring) {
            confidence += 0.2;
        }
        // 有地点和时间要求，提高置信度
        if (analysis.hasLocationSpecificWords && analysis.hasTimePatterns) {
            confidence += 0.2;
        }
        // 只有截止日期但没有其他时间指示器，降低置信度
        if (analysis.hasDeadline && !analysis.hasTimePatterns && !analysis.hasTimeSpecificWords) {
            confidence -= 0.1;
        }
        return Math.min(1.0, Math.max(constants_1.CONFIDENCE_THRESHOLDS.LOW, confidence));
    }
}
exports.CalendarRule = CalendarRule;
//# sourceMappingURL=calendar-rule.js.map