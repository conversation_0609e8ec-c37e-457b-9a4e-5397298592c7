import { DecisionRule, RuleResult } from '../../types';
import { DecisionContext } from '../decision-context';
/**
 * 责任归属规则
 * 判断任务是否该由自己执行，还是需要委派给他人
 */
export declare class OwnershipRule implements DecisionRule {
    readonly name = "OwnershipRule";
    readonly priority: 3;
    evaluate(context: DecisionContext): Promise<RuleResult>;
    /**
     * 分析任务的责任归属
     */
    private analyzeOwnership;
    /**
     * 提取明确的人员信息
     */
    private extractExplicitPerson;
    /**
     * 判断责任归属
     */
    private determineOwnership;
    /**
     * 检查是否为典型的委派任务
     */
    private isTypicallyDelegated;
    /**
     * 检查是否为典型的个人任务
     */
    private isTypicallyPersonal;
    /**
     * 构建推理过程
     */
    private buildReasoning;
    /**
     * 计算置信度
     */
    private calculateConfidence;
}
//# sourceMappingURL=ownership-rule.d.ts.map