export * from './decision-context';
export * from './decision-tree';
export * from './rules/actionability-rule';
export * from './rules/time-rule';
export * from './rules/ownership-rule';
export * from './rules/complexity-rule';
export * from './rules/calendar-rule';
import { DecisionTree } from './decision-tree';
/**
 * 决策引擎工厂类
 * 提供便捷的决策引擎创建和配置方法
 */
export declare class DecisionEngineFactory {
    /**
     * 创建标准的GTD决策引擎
     * 包含所有核心决策规则
     */
    static createStandardEngine(logger?: (level: string, message: string, data?: any) => void): DecisionTree;
    /**
     * 创建自定义决策引擎
     * 允许指定特定的规则集合
     */
    static createCustomEngine(rules: Array<new () => any>, logger?: (level: string, message: string, data?: any) => void): DecisionTree;
    /**
     * 创建最小化决策引擎
     * 只包含最基本的决策规则
     */
    static createMinimalEngine(logger?: (level: string, message: string, data?: any) => void): DecisionTree;
    /**
     * 获取所有可用的规则类
     */
    static getAvailableRules(): Array<new () => any>;
    /**
     * 获取规则信息
     */
    static getRuleInfo(): Array<{
        name: string;
        priority: number;
        description: string;
    }>;
}
//# sourceMappingURL=index.d.ts.map