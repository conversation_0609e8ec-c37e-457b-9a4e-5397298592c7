"use strict";
// GTD 明晰模块常量定义
Object.defineProperty(exports, "__esModule", { value: true });
exports.EVENT_TYPES = exports.LOG_LEVELS = exports.ERROR_MESSAGES = exports.DEFAULT_CONFIG = exports.TARGET_LIST_MAPPING = exports.REFERENCE_KEYWORDS = exports.CALENDAR_KEYWORDS = exports.PROJECT_KEYWORDS = exports.DELEGATION_KEYWORDS = exports.ACTIONABILITY_KEYWORDS = exports.TIME_CONSTANTS = exports.CONFIDENCE_THRESHOLDS = exports.RULE_PRIORITIES = void 0;
// 重用共享常量，避免重复定义
const gtd_constants_1 = require("../shared/gtd-constants");
/**
 * 决策规则优先级
 */
exports.RULE_PRIORITIES = {
    ACTIONABILITY: 1, // 可行动性检查 - 最高优先级
    TIME: 2, // 时间判断 (2分钟规则)
    OWNERSHIP: 3, // 责任归属判断
    COMPLEXITY: 4, // 复杂度判断 (单一行动 vs 项目)
    CALENDAR: 5 // 日历判断 - 最低优先级
};
/**
 * 决策置信度阈值 (扩展共享常量)
 */
exports.CONFIDENCE_THRESHOLDS = {
    ...gtd_constants_1.CONFIDENCE_THRESHOLDS,
    MINIMUM: 0.2 // 最低可接受置信度
};
/**
 * 时间相关常量
 */
exports.TIME_CONSTANTS = {
    TWO_MINUTE_THRESHOLD: 2, // 2分钟阈值(分钟)
    QUICK_ACTION_MAX_MINUTES: 5, // 快速行动最大时长(分钟)
    DEFAULT_TASK_ESTIMATE: 30, // 默认任务预估时长(分钟)
    DEFAULT_PROJECT_ESTIMATE: 120, // 默认项目预估时长(分钟)
    FOLLOW_UP_DEFAULT_DAYS: 7 // 默认跟进天数
};
/**
 * 可行动性检查关键词 (扩展共享常量)
 */
exports.ACTIONABILITY_KEYWORDS = {
    // 明确的行动词汇 (扩展共享的ACTION_WORDS)
    ACTION_VERBS: [
        ...gtd_constants_1.ACTIONABILITY_KEYWORDS.ACTION_WORDS,
        '下载', '上传', '安装', '删除', '修改', '检查', '确认',
        '回复', '转发', '分享', '保存', '备份', '更新'
    ],
    // 模糊的思考词汇 (扩展共享的VAGUE_WORDS)
    VAGUE_VERBS: [
        ...gtd_constants_1.ACTIONABILITY_KEYWORDS.VAGUE_WORDS,
        '探索', '分析', '评估', '观察', '关注', '留意', '注意', '记住', '想想', '看看'
    ],
    // 多步骤项目词汇 (扩展共享的MULTI_STEP_WORDS)
    PROJECT_VERBS: [
        ...gtd_constants_1.ACTIONABILITY_KEYWORDS.MULTI_STEP_WORDS,
        '推进', '启动', '完善', '优化', '改进', '重构'
    ],
    // 非行动性词汇
    NON_ACTION_WORDS: [
        '信息', '资料', '文档', '参考', '记录', '备忘', '提醒', '想法',
        '灵感', '点子', '建议', '意见', '反馈', '评论', '笔记'
    ]
};
/**
 * 委派相关关键词
 */
exports.DELEGATION_KEYWORDS = {
    DELEGATION_INDICATORS: [
        '让', '请', '委托', '交给', '分配给', '安排', '指派', '转交',
        '找', '联系', '询问', '咨询', '协调', '配合', '合作'
    ],
    PERSON_INDICATORS: [
        '同事', '老板', '下属', '客户', '供应商', '朋友', '家人',
        '医生', '律师', '会计', '设计师', '开发者', '销售'
    ]
};
/**
 * 项目识别关键词
 */
exports.PROJECT_KEYWORDS = {
    PROJECT_INDICATORS: [
        '项目', '计划', '方案', '策略', '系统', '流程', '活动', '事件',
        '培训', '课程', '学习', '研究', '调研', '分析', '评估', '审核'
    ],
    OUTCOME_INDICATORS: [
        '完成', '实现', '达到', '获得', '建立', '创建', '开发', '设计',
        '提升', '改善', '优化', '解决', '处理', '搞定'
    ]
};
/**
 * 日历事件关键词
 */
exports.CALENDAR_KEYWORDS = {
    TIME_SPECIFIC: [
        '会议', '约会', '面试', '培训', '课程', '讲座', '演讲', '展示',
        '聚会', '聚餐', '活动', '庆祝', '仪式', '典礼', '婚礼', '生日'
    ],
    LOCATION_SPECIFIC: [
        '在', '去', '到', '前往', '出差', '旅行', '拜访', '参观', '检查'
    ],
    TIME_PATTERNS: [
        /(\d{1,2})[点时:：](\d{0,2})/, // 时间模式: 9点, 14:30
        /(\d{1,2})月(\d{1,2})[日号]/, // 日期模式: 3月15日
        /周[一二三四五六日天]/, // 星期模式
        /(明天|后天|下周|下月)/ // 相对时间
    ]
};
/**
 * 参考资料关键词
 */
exports.REFERENCE_KEYWORDS = {
    REFERENCE_INDICATORS: [
        '资料', '文档', '文件', '报告', '手册', '指南', '教程', '说明',
        '规范', '标准', '模板', '示例', '案例', '经验', '知识', '信息'
    ],
    STORAGE_INDICATORS: [
        '保存', '存储', '归档', '收藏', '备份', '记录', '整理', '分类'
    ]
};
/**
 * 目标清单映射
 */
exports.TARGET_LIST_MAPPING = {
    'actionable_quick': 'next-actions', // 可行动 + 快速
    'actionable_delegate': 'waiting', // 可行动 + 委派
    'actionable_single': 'next-actions', // 可行动 + 单一行动
    'actionable_project': 'projects', // 可行动 + 项目
    'actionable_calendar': 'calendar', // 可行动 + 日历
    'non_actionable_trash': 'trash', // 非行动 + 垃圾
    'non_actionable_reference': 'reference', // 非行动 + 参考
    'non_actionable_someday': 'incubator' // 非行动 + 将来/也许
};
/**
 * 默认配置
 */
exports.DEFAULT_CONFIG = {
    // 用户偏好默认值
    USER_PREFERENCES: {
        defaultCategory: 'personal',
        twoMinuteThreshold: 2,
        autoAssignContexts: true,
        preferredContexts: ['@电脑', '@电话', '@外出']
    },
    // 批量处理默认值
    BATCH_PROCESSING: {
        maxConcurrent: 5,
        stopOnError: false,
        userInteraction: false
    },
    // 性能相关
    PERFORMANCE: {
        maxProcessingTime: 30000, // 最大处理时间(ms)
        cacheTimeout: 300000, // 缓存超时时间(ms)
        retryAttempts: 3 // 重试次数
    }
};
/**
 * 错误消息
 */
exports.ERROR_MESSAGES = {
    INVALID_INBOX_ITEM: '无效的收集箱条目',
    DECISION_ENGINE_ERROR: '决策引擎执行错误',
    RULE_EXECUTION_ERROR: '规则执行错误',
    INSUFFICIENT_CONFIDENCE: '决策置信度不足',
    TIMEOUT_ERROR: '处理超时',
    UNKNOWN_TARGET_LIST: '未知的目标清单类型'
};
/**
 * 日志级别
 */
exports.LOG_LEVELS = {
    ERROR: 'error',
    WARN: 'warn',
    INFO: 'info',
    DEBUG: 'debug'
};
/**
 * 事件类型
 */
exports.EVENT_TYPES = {
    CLARIFY_STARTED: 'clarify_started',
    CLARIFY_COMPLETED: 'clarify_completed',
    CLARIFY_FAILED: 'clarify_failed',
    RULE_APPLIED: 'rule_applied',
    DECISION_MADE: 'decision_made'
};
//# sourceMappingURL=constants.js.map