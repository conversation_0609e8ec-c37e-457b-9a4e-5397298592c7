// GTD 明晰模块专用类型定义

import { InboxItem, Task, Project, TaskCategory } from '../../types';

/**
 * 目标清单类型
 */
export type TargetList = 
  | 'trash'           // 垃圾箱
  | 'reference'       // 知识库  
  | 'incubator'       // 孵化池
  | 'next-actions'    // 执行清单
  | 'waiting'         // 等待清单
  | 'projects'        // 项目清单
  | 'calendar';       // 日历

/**
 * 明晰决策结果
 */
export interface ClarifyDecision {
  // === 决策路径 ===
  isActionable: boolean;                    // 可行动吗？
  canDoInTwoMinutes?: boolean;             // 2分钟内能搞定？
  isMyResponsibility?: boolean;            // 该我做吗？
  isSingleAction?: boolean;                // 是单一行动吗？
  hasSpecificTime?: boolean;               // 有特定时间吗？
  
  // === 决策结果 ===
  targetList: TargetList;                  // 目标清单
  quickActionSuggestion?: boolean;         // 建议立即执行
  
  // === 元数据 ===
  reasoning: string[];                     // 决策推理过程
  confidence: number;                      // 决策置信度 (0-1)
  appliedRules: string[];                  // 应用的规则列表
}

/**
 * 明晰处理结果
 */
export interface ClarifyResult {
  decision: ClarifyDecision;               // 决策信息
  createdItems: ClarifiedItem[];           // 创建的条目
  updatedInboxItem: InboxItem;             // 更新后的收集箱条目
  processingTime: number;                  // 处理耗时(ms)
}

/**
 * 明晰后创建的条目
 */
export interface ClarifiedItem {
  type: 'task' | 'project' | 'waiting' | 'reference' | 'calendar';
  data: any;                               // 具体数据
  targetList: TargetList;                  // 目标清单
  id: string;                              // 创建的条目ID
}

/**
 * 等待清单条目
 */
export interface WaitingItem {
  id: string;
  description: string;                     // 等待事项描述
  delegatedTo: string;                     // 委派给谁
  expectedDate?: Date;                     // 期望完成日期
  followUpDate: Date;                      // 跟进日期
  context?: string;                        // 情境
  relatedProjectId?: string;               // 关联项目ID
  notes?: string;                          // 备注
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 参考资料条目
 */
export interface ReferenceItem {
  id: string;
  title: string;                           // 标题
  content: string;                         // 内容
  tags: string[];                          // 标签
  category: string;                        // 分类
  relatedProjectId?: string;               // 关联项目ID
  url?: string;                            // 相关链接
  attachments?: string[];                  // 附件
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 孵化池条目
 */
export interface IncubatorItem {
  id: string;
  description: string;                     // 描述
  type: 'someday-maybe' | 'future-project'; // 类型：将来/也许 或 未来项目
  category?: TaskCategory;                 // 分类
  reviewDate?: Date;                       // 下次回顾日期
  notes?: string;                          // 备注
  tags: string[];                          // 标签
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 日历条目
 */
export interface CalendarItem {
  id: string;
  title: string;                           // 标题
  description?: string;                    // 描述
  startTime: Date;                         // 开始时间
  endTime?: Date;                          // 结束时间
  location?: string;                       // 地点
  attendees?: string[];                    // 参与者
  isAllDay: boolean;                       // 是否全天
  category?: TaskCategory;                 // 分类
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 决策上下文接口
 */
export interface IDecisionContext {
  item: InboxItem;                         // 待处理的收集箱条目
  userPreferences?: UserPreferences;       // 用户偏好
  currentWorkload?: WorkloadInfo;          // 当前工作负载
  timeContext?: TimeContext;               // 时间上下文
}

/**
 * 用户偏好设置
 */
export interface UserPreferences {
  defaultCategory: TaskCategory;           // 默认分类
  twoMinuteThreshold: number;             // 2分钟阈值(分钟)
  autoAssignContexts: boolean;            // 自动分配情境
  preferredContexts: string[];            // 偏好情境
}

/**
 * 工作负载信息
 */
export interface WorkloadInfo {
  activeTasks: number;                     // 活跃任务数
  activeProjects: number;                  // 活跃项目数
  upcomingDeadlines: number;              // 即将到期的任务数
  availableTime: number;                   // 可用时间(分钟)
}

/**
 * 时间上下文
 */
export interface TimeContext {
  currentTime: Date;                       // 当前时间
  workingHours: {                         // 工作时间
    start: string;                         // 开始时间 HH:mm
    end: string;                           // 结束时间 HH:mm
  };
  isWorkingTime: boolean;                 // 是否工作时间
  timeZone: string;                       // 时区
}

/**
 * 决策规则接口
 */
export interface DecisionRule {
  name: string;                           // 规则名称
  priority: number;                       // 优先级 (数字越小优先级越高)
  evaluate(context: IDecisionContext): Promise<RuleResult>;
}

/**
 * 规则执行结果
 */
export interface RuleResult {
  ruleName: string;                       // 规则名称
  decision?: Partial<ClarifyDecision>;    // 决策建议
  confidence: number;                     // 置信度
  reasoning: string[];                    // 推理过程
  shouldContinue: boolean;                // 是否继续执行后续规则
}

/**
 * 批量明晰选项
 */
export interface BatchClarifyOptions {
  maxConcurrent?: number;                 // 最大并发数
  stopOnError?: boolean;                  // 遇到错误时停止
  userInteraction?: boolean;              // 是否需要用户交互
}

/**
 * 批量明晰结果
 */
export interface BatchClarifyResult {
  results: ClarifyResult[];               // 处理结果
  errors: Array<{                         // 错误信息
    itemId: string;
    error: string;
  }>;
  summary: {                              // 汇总信息
    total: number;
    successful: number;
    failed: number;
    processingTime: number;
  };
}
