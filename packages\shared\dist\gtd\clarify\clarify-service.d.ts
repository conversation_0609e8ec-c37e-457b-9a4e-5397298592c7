import { ClarifyResult, BatchClarifyOptions, BatchClarifyResult } from './types';
import { InboxRepository, TaskRepository, ProjectRepository } from '../../types/database';
import { DecisionTree } from './decision-engine/decision-tree';
/**
 * GTD 明晰服务
 * 负责协调整个明晰流程，从决策到实际创建目标条目
 */
export declare class ClarifyService {
    private inboxRepository;
    private taskRepository;
    private projectRepository;
    private decisionEngine;
    private logger?;
    private eventListeners;
    constructor(inboxRepository: InboxRepository, taskRepository: TaskRepository, projectRepository: ProjectRepository, decisionEngine: DecisionTree, logger?: ((level: string, message: string, data?: any) => void) | undefined);
    /**
     * 明晰单个收集箱条目
     */
    clarifyItem(itemId: string): Promise<ClarifyResult>;
    /**
     * 批量明晰收集箱条目
     */
    batchClarify(itemIds: string[], options?: BatchClarifyOptions): Promise<BatchClarifyResult>;
    /**
     * 根据决策创建目标条目
     */
    private createTargetItems;
    /**
     * 创建任务
     */
    private createTask;
    /**
     * 创建项目（包含下一步行动）
     */
    private createProject;
    private createWaitingItem;
    private createCalendarItem;
    private createReferenceItem;
    private createIncubatorItem;
    /**
     * 从描述中提取情境
     */
    private extractContexts;
    /**
     * 添加事件监听器
     */
    on(event: string, listener: Function): void;
    /**
     * 触发事件
     */
    private emit;
    /**
     * 日志记录
     */
    private log;
}
//# sourceMappingURL=clarify-service.d.ts.map