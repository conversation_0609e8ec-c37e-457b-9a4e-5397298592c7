import { InboxRepository, InboxQueryFilter, QueryOptions, DatabaseConnection } from '../types/database';
import { InboxItem, InboxStatus } from '../types';
/**
 * SQLite 收集箱仓储实现
 */
export declare class SQLiteInboxRepository implements InboxRepository {
    private connection;
    constructor(connection: DatabaseConnection);
    /**
     * 创建新条目
     */
    create(item: Partial<InboxItem>): Promise<InboxItem>;
    /**
     * 保存收集箱条目
     */
    save(item: InboxItem): Promise<string>;
    /**
     * 根据ID查找条目
     */
    findById(id: string): Promise<InboxItem | null>;
    /**
     * 查找所有条目
     */
    findAll(options?: QueryOptions): Promise<InboxItem[]>;
    /**
     * 更新条目
     */
    update(id: string, updates: Partial<InboxItem>): Promise<InboxItem>;
    /**
     * 删除条目
     */
    delete(id: string): Promise<void>;
    /**
     * 根据状态查找条目
     */
    findByStatus(status: InboxStatus, options?: QueryOptions): Promise<InboxItem[]>;
    /**
     * 根据过滤器查找条目
     */
    findByFilter(filter: InboxQueryFilter, options?: QueryOptions): Promise<InboxItem[]>;
    /**
     * 统计条目数量
     */
    count(filter?: InboxQueryFilter): Promise<number>;
    /**
     * 批量保存条目
     */
    saveMany(items: InboxItem[]): Promise<string[]>;
    /**
     * 批量更新条目
     */
    updateMany(updates: Array<{
        id: string;
        data: Partial<InboxItem>;
    }>): Promise<void>;
    /**
     * 批量删除条目
     */
    deleteMany(ids: string[]): Promise<void>;
    /**
     * 初始化仓储
     */
    initialize(): Promise<void>;
    /**
     * 关闭仓储
     */
    close(): Promise<void>;
    /**
     * 数据库清理
     */
    vacuum(): Promise<void>;
    /**
     * 将数据库记录映射为领域对象
     */
    private mapRecordToItem;
    /**
     * 构建 WHERE 子句
     */
    private buildWhereClause;
}
//# sourceMappingURL=inbox-repository.d.ts.map