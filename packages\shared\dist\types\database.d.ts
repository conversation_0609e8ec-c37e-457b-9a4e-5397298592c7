import { InboxItem, InboxStatus, CaptureSource, TaskCategory, Task, TaskStatus, CreateTaskInput, Project, ProjectStatus, CreateProjectInput } from './index';
export interface DatabaseConfig {
    path: string;
    enableWAL?: boolean;
    enableForeignKeys?: boolean;
    busyTimeout?: number;
}
export interface DatabaseConnection {
    run(sql: string, params?: any[]): Promise<{
        lastID: number;
        changes: number;
    }>;
    get<T = any>(sql: string, params?: any[]): Promise<T | undefined>;
    all<T = any>(sql: string, params?: any[]): Promise<T[]>;
    close(): Promise<void>;
    beginTransaction(): Promise<void>;
    commit(): Promise<void>;
    rollback(): Promise<void>;
}
export interface InboxItemRecord {
    id: string;
    description: string;
    expected_outcome?: string;
    deadline?: string;
    estimated_minutes?: number;
    category?: TaskCategory;
    source: CaptureSource;
    status: InboxStatus;
    raw_input?: string;
    ai_suggestions?: string;
    created_at: string;
    updated_at: string;
    version: number;
}
export interface QueryOptions {
    limit?: number;
    offset?: number;
    orderBy?: string;
    orderDirection?: 'ASC' | 'DESC';
}
export interface InboxQueryFilter {
    status?: InboxStatus;
    source?: CaptureSource;
    category?: TaskCategory;
    createdAfter?: Date;
    createdBefore?: Date;
    hasDeadline?: boolean;
}
export interface InboxRepository {
    save(item: InboxItem): Promise<string>;
    findById(id: string): Promise<InboxItem | null>;
    findAll(options?: QueryOptions): Promise<InboxItem[]>;
    update(id: string, updates: Partial<InboxItem>): Promise<InboxItem>;
    delete(id: string): Promise<void>;
    create(item: Partial<InboxItem>): Promise<InboxItem>;
    findByStatus(status: InboxStatus, options?: QueryOptions): Promise<InboxItem[]>;
    findByFilter(filter: InboxQueryFilter, options?: QueryOptions): Promise<InboxItem[]>;
    count(filter?: InboxQueryFilter): Promise<number>;
    saveMany(items: InboxItem[]): Promise<string[]>;
    updateMany(updates: Array<{
        id: string;
        data: Partial<InboxItem>;
    }>): Promise<void>;
    deleteMany(ids: string[]): Promise<void>;
    initialize(): Promise<void>;
    close(): Promise<void>;
    vacuum(): Promise<void>;
}
export interface TaskRepository {
    findById(id: string): Promise<Task | null>;
    findAll(options?: QueryOptions): Promise<Task[]>;
    create(task: CreateTaskInput): Promise<Task>;
    update(id: string, updates: Partial<Task>): Promise<Task>;
    delete(id: string): Promise<void>;
    findByStatus(status: TaskStatus, options?: QueryOptions): Promise<Task[]>;
    findByCategory(category: TaskCategory, options?: QueryOptions): Promise<Task[]>;
    findByProject(projectId: string, options?: QueryOptions): Promise<Task[]>;
    count(): Promise<number>;
    countByStatus(): Promise<Record<TaskStatus, number>>;
}
export interface ProjectRepository {
    findById(id: string): Promise<Project | null>;
    findAll(options?: QueryOptions): Promise<Project[]>;
    create(project: CreateProjectInput): Promise<Project>;
    update(id: string, updates: Partial<Project>): Promise<Project>;
    delete(id: string): Promise<void>;
    findByStatus(status: ProjectStatus, options?: QueryOptions): Promise<Project[]>;
    findByCategory(category: TaskCategory, options?: QueryOptions): Promise<Project[]>;
    count(): Promise<number>;
    countByStatus(): Promise<Record<ProjectStatus, number>>;
}
//# sourceMappingURL=database.d.ts.map