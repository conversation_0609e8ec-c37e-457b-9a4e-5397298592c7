# FocusGuard 项目搭建指南

## 项目创建步骤

### 1. Git 仓库创建建议

**推荐步骤（本地优先）**：

1. **本地项目已创建** ✅ - 基础项目结构已搭建完成
2. **本地Git初始化** - 执行以下命令：
   ```bash
   git init
   git add .
   git commit -m "Initial commit: 项目基础结构"
   ```

3. **Gitee创建远程仓库**：
   - 登录 Gitee
   - 创建新仓库 `FocusGuard`
   - **重要**: 不要勾选"使用Readme文件初始化这个仓库"
   - 不要选择 .gitignore 模板（我们已经创建了）
   - 不要选择开源许可证（我们已经在package.json中指定了）

4. **关联并推送**：
   ```bash
   git remote add origin https://gitee.com/trjspace/focusguard.git
   git branch -M main
   git push -u origin main
   ```

### 2. 当前项目结构

```
FocusGuard/
├── README.md                       # 项目说明
├── package.json                    # 根配置文件（workspace）
├── tsconfig.json                   # TypeScript配置
├── .gitignore                      # Git忽略文件
├── .env.example                    # 环境变量示例
├── PROJECT_SETUP_GUIDE.md          # 本文档
│
├── docs/                           # 项目文档
│   ├── README.md                   # 文档目录
│   ├── PROJECT_STRUCTURE.md        # 项目结构设计
│   ├── ARCHITECTURE.md             # 技术架构设计
│   ├── DEVELOPMENT_PLAN.md         # 开发计划
│   ├── API_DESIGN.md               # API设计文档
│   ├── DATABASE_DESIGN.md          # 数据库设计
│   └── TECH_STACK_COMPARISON.md    # 技术栈对比
│
└── packages/                       # 共享包
    └── shared/                     # 核心业务逻辑
        ├── package.json            # 包配置
        ├── tsconfig.json           # TypeScript配置
        └── src/                    # 源代码
            ├── index.ts            # 入口文件
            ├── types/              # 类型定义
            ├── constants/          # 常量定义
            ├── utils/              # 工具函数
            └── gtd/                # GTD核心逻辑
```

### 3. 已完成的工作

✅ **基础配置**：
- Monorepo workspace 配置
- TypeScript 配置和路径映射
- ESLint 和 Prettier 配置
- Git 忽略文件
- 环境变量模板

✅ **共享包 (@focusguard/shared)**：
- 完整的 TypeScript 类型定义
- 常量定义（状态、分类、API端点等）
- 工具函数（日期、时间、字符串、数组、验证等）
- GTD 核心逻辑（捕获、明晰、组织、执行）

✅ **项目文档**：
- 详细的项目结构设计
- 技术架构设计
- 开发计划和里程碑
- API 设计规范
- 数据库设计方案
- 技术栈选择对比

## 下一步开发计划

### 第一阶段：Web应用基础框架（1-2周）

1. **创建Web应用结构**：
   ```bash
   mkdir -p apps/web/src/{pages,components,hooks,styles}
   ```

2. **Web应用配置**：
   - Vite + React 配置
   - 路由配置 (React Router)
   - 状态管理 (Zustand)
   - UI组件库选择

3. **基础页面**：
   - 登录/注册页面
   - 仪表板页面
   - 任务列表页面
   - 设置页面

### 第二阶段：API服务基础框架（1-2周）

1. **创建API服务结构**：
   ```bash
   mkdir -p services/api/src/{routes,controllers,services,models,middleware,config}
   ```

2. **API服务配置**：
   - Express.js 服务器配置
   - 数据库连接 (PostgreSQL)
   - JWT 认证中间件
   - 数据验证中间件

3. **基础API端点**：
   - 用户认证 API
   - 任务 CRUD API
   - 项目 CRUD API

### 第三阶段：核心功能开发（2-3周）

1. **任务管理功能**：
   - 任务创建、编辑、删除
   - 任务状态管理
   - 任务分类和标签

2. **GTD流程实现**：
   - 快速捕获界面
   - 任务明晰流程
   - 情境管理

3. **数据同步**：
   - 本地存储
   - 云端同步
   - 冲突处理

## 开发环境搭建

### 1. 安装依赖

```bash
# 在项目根目录执行
npm install
```

### 2. 环境配置

```bash
# 复制环境变量文件
cp .env.example .env

# 编辑 .env 文件，配置：
# - 数据库连接信息
# - JWT 密钥
# - API 端口配置
```

### 3. 数据库准备

```bash
# 安装 PostgreSQL
# 创建数据库
createdb focusguard_dev

# 安装 Redis（用于缓存）
# 启动 Redis 服务
```

### 4. 开发服务启动

```bash
# 启动所有服务
npm run dev

# 或分别启动
npm run web:dev    # Web应用 (http://localhost:3000)
npm run api:dev    # API服务 (http://localhost:3001)
```

## 技术栈确认

✅ **统一 TypeScript 技术栈**：
- **前端**: React + TypeScript + Vite
- **后端**: Node.js + Express + TypeScript
- **数据库**: PostgreSQL + SQLite
- **状态管理**: Zustand
- **认证**: JWT
- **开发工具**: VS Code

## 开发规范

### 代码规范
```bash
# 代码检查
npm run lint

# 自动修复
npm run lint:fix

# 类型检查
npm run type-check
```

### 提交规范
```bash
# 提交格式
git commit -m "feat: 添加任务创建功能"
git commit -m "fix: 修复任务状态更新问题"
git commit -m "docs: 更新API文档"
```

### 分支管理
```bash
# 功能开发
git checkout -b feature/task-management
git checkout -b feature/user-auth

# 修复问题
git checkout -b fix/task-status-bug

# 文档更新
git checkout -b docs/api-update
```

## 项目特色

🎯 **GTD+ 理念**：
- 系统性任务管理
- 价值驱动执行
- 幸福感守护机制

🛠️ **技术优势**：
- 统一 TypeScript 技术栈
- 类型安全的前后端通信
- 模块化的代码组织
- 完善的开发工具链

📚 **完善文档**：
- 详细的架构设计
- 清晰的开发计划
- 规范的API设计
- 完整的数据库设计

## 联系与支持

- **项目地址**: https://gitee.com/your-username/focusguard
- **问题反馈**: https://gitee.com/your-username/focusguard/issues
- **开发文档**: ./docs/

---

**准备好开始构建 FocusGuard 了吗？让我们从创建 Git 仓库开始！** 🚀
