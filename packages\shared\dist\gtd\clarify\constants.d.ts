import { TargetList } from './types';
/**
 * 决策规则优先级
 */
export declare const RULE_PRIORITIES: {
    readonly ACTIONABILITY: 1;
    readonly TIME: 2;
    readonly OWNERSHIP: 3;
    readonly COMPLEXITY: 4;
    readonly CALENDAR: 5;
};
/**
 * 决策置信度阈值 (扩展共享常量)
 */
export declare const CONFIDENCE_THRESHOLDS: {
    readonly MINIMUM: 0.2;
    readonly HIGH: number;
    readonly MEDIUM: number;
    readonly LOW: number;
};
/**
 * 时间相关常量
 */
export declare const TIME_CONSTANTS: {
    readonly TWO_MINUTE_THRESHOLD: 2;
    readonly QUICK_ACTION_MAX_MINUTES: 5;
    readonly DEFAULT_TASK_ESTIMATE: 30;
    readonly DEFAULT_PROJECT_ESTIMATE: 120;
    readonly FOLLOW_UP_DEFAULT_DAYS: 7;
};
/**
 * 可行动性检查关键词 (扩展共享常量)
 */
export declare const ACTIONABILITY_KEYWORDS: {
    readonly ACTION_VERBS: readonly [...string[], "下载", "上传", "安装", "删除", "修改", "检查", "确认", "回复", "转发", "分享", "保存", "备份", "更新"];
    readonly VAGUE_VERBS: readonly [...string[], "探索", "分析", "评估", "观察", "关注", "留意", "注意", "记住", "想想", "看看"];
    readonly PROJECT_VERBS: readonly [...string[], "推进", "启动", "完善", "优化", "改进", "重构"];
    readonly NON_ACTION_WORDS: readonly ["信息", "资料", "文档", "参考", "记录", "备忘", "提醒", "想法", "灵感", "点子", "建议", "意见", "反馈", "评论", "笔记"];
};
/**
 * 委派相关关键词
 */
export declare const DELEGATION_KEYWORDS: {
    readonly DELEGATION_INDICATORS: readonly ["让", "请", "委托", "交给", "分配给", "安排", "指派", "转交", "找", "联系", "询问", "咨询", "协调", "配合", "合作"];
    readonly PERSON_INDICATORS: readonly ["同事", "老板", "下属", "客户", "供应商", "朋友", "家人", "医生", "律师", "会计", "设计师", "开发者", "销售"];
};
/**
 * 项目识别关键词
 */
export declare const PROJECT_KEYWORDS: {
    readonly PROJECT_INDICATORS: readonly ["项目", "计划", "方案", "策略", "系统", "流程", "活动", "事件", "培训", "课程", "学习", "研究", "调研", "分析", "评估", "审核"];
    readonly OUTCOME_INDICATORS: readonly ["完成", "实现", "达到", "获得", "建立", "创建", "开发", "设计", "提升", "改善", "优化", "解决", "处理", "搞定"];
};
/**
 * 日历事件关键词
 */
export declare const CALENDAR_KEYWORDS: {
    readonly TIME_SPECIFIC: readonly ["会议", "约会", "面试", "培训", "课程", "讲座", "演讲", "展示", "聚会", "聚餐", "活动", "庆祝", "仪式", "典礼", "婚礼", "生日"];
    readonly LOCATION_SPECIFIC: readonly ["在", "去", "到", "前往", "出差", "旅行", "拜访", "参观", "检查"];
    readonly TIME_PATTERNS: readonly [RegExp, RegExp, RegExp, RegExp];
};
/**
 * 参考资料关键词
 */
export declare const REFERENCE_KEYWORDS: {
    readonly REFERENCE_INDICATORS: readonly ["资料", "文档", "文件", "报告", "手册", "指南", "教程", "说明", "规范", "标准", "模板", "示例", "案例", "经验", "知识", "信息"];
    readonly STORAGE_INDICATORS: readonly ["保存", "存储", "归档", "收藏", "备份", "记录", "整理", "分类"];
};
/**
 * 目标清单映射
 */
export declare const TARGET_LIST_MAPPING: Record<string, TargetList>;
/**
 * 默认配置
 */
export declare const DEFAULT_CONFIG: {
    readonly USER_PREFERENCES: {
        readonly defaultCategory: "personal";
        readonly twoMinuteThreshold: 2;
        readonly autoAssignContexts: true;
        readonly preferredContexts: string[];
    };
    readonly BATCH_PROCESSING: {
        readonly maxConcurrent: 5;
        readonly stopOnError: false;
        readonly userInteraction: false;
    };
    readonly PERFORMANCE: {
        readonly maxProcessingTime: 30000;
        readonly cacheTimeout: 300000;
        readonly retryAttempts: 3;
    };
};
/**
 * 错误消息
 */
export declare const ERROR_MESSAGES: {
    readonly INVALID_INBOX_ITEM: "无效的收集箱条目";
    readonly DECISION_ENGINE_ERROR: "决策引擎执行错误";
    readonly RULE_EXECUTION_ERROR: "规则执行错误";
    readonly INSUFFICIENT_CONFIDENCE: "决策置信度不足";
    readonly TIMEOUT_ERROR: "处理超时";
    readonly UNKNOWN_TARGET_LIST: "未知的目标清单类型";
};
/**
 * 日志级别
 */
export declare const LOG_LEVELS: {
    readonly ERROR: "error";
    readonly WARN: "warn";
    readonly INFO: "info";
    readonly DEBUG: "debug";
};
/**
 * 事件类型
 */
export declare const EVENT_TYPES: {
    readonly CLARIFY_STARTED: "clarify_started";
    readonly CLARIFY_COMPLETED: "clarify_completed";
    readonly CLARIFY_FAILED: "clarify_failed";
    readonly RULE_APPLIED: "rule_applied";
    readonly DECISION_MADE: "decision_made";
};
//# sourceMappingURL=constants.d.ts.map