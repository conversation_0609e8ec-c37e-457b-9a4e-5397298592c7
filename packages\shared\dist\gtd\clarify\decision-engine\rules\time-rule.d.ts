import { DecisionRule, RuleResult } from '../../types';
import { DecisionContext } from '../decision-context';
/**
 * 时间规则 - 实现GTD的2分钟规则
 * 如果一个任务能在2分钟内完成，建议立即执行
 */
export declare class TimeRule implements DecisionRule {
    readonly name = "TimeRule";
    readonly priority: 2;
    evaluate(context: DecisionContext): Promise<RuleResult>;
    /**
     * 获取或估算任务时间
     */
    private getEstimatedTime;
    /**
     * 基于描述估算时间
     */
    private estimateTimeFromDescription;
    /**
     * 根据时间指示词调整估算
     */
    private adjustForTimeIndicators;
    /**
     * 构建推理过程
     */
    private buildReasoning;
    /**
     * 计算置信度
     */
    private calculateConfidence;
}
//# sourceMappingURL=time-rule.d.ts.map