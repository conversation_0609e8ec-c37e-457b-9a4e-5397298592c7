{"version": 3, "file": "inboxRoutes.js", "sourceRoot": "", "sources": ["../../src/routes/inboxRoutes.ts"], "names": [], "mappings": ";;AAMA,4CAyNC;AA5ND;;GAEG;AACH,SAAgB,gBAAgB,CAAC,GAAY,EAAE,MAAc,EAAE,eAAgC;IAC7F,MAAM,eAAe,GAAG,eAAe,CAAC,kBAAkB,EAAE,CAAA;IAE5D;;OAEG;IACH,GAAG,CAAC,GAAG,CAAC,GAAG,MAAM,cAAc,EAAE,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;QACrE,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,MAAM,eAAe,CAAC,KAAK,EAAE,CAAA;YAChD,MAAM,aAAa,GAAG,MAAM,eAAe,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC,CAAA;YACzE,MAAM,eAAe,GAAG,MAAM,eAAe,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,YAAY,EAAE,CAAC,CAAA;YAC7E,MAAM,cAAc,GAAG,MAAM,eAAe,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC,CAAA;YAE3E,GAAG,CAAC,IAAI,CAAC;gBACP,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ,KAAK,EAAE,UAAU;oBACjB,QAAQ,EAAE,aAAa;oBACvB,UAAU,EAAE,eAAe;oBAC3B,SAAS,EAAE,cAAc;iBAC1B;aACF,CAAC,CAAA;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAA;YAClC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,SAAS;aACjB,CAAC,CAAA;QACJ,CAAC;IACH,CAAC,CAAC,CAAA;IAEF;;OAEG;IACH,GAAG,CAAC,GAAG,CAAC,GAAG,MAAM,QAAQ,EAAE,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;QAC/D,IAAI,CAAC;YACH,MAAM,EAAE,MAAM,EAAE,KAAK,GAAG,EAAE,EAAE,MAAM,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,KAAK,CAAA;YAEpD,IAAI,KAAK,CAAA;YACT,IAAI,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;gBACzC,KAAK,GAAG,MAAM,eAAe,CAAC,YAAY,CAAC,MAAa,EAAE;oBACxD,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC;oBACpB,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC;oBACtB,OAAO,EAAE,YAAY;oBACrB,cAAc,EAAE,MAAM;iBACvB,CAAC,CAAA;YACJ,CAAC;iBAAM,CAAC;gBACN,KAAK,GAAG,MAAM,eAAe,CAAC,OAAO,CAAC;oBACpC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC;oBACpB,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC;oBACtB,OAAO,EAAE,YAAY;oBACrB,cAAc,EAAE,MAAM;iBACvB,CAAC,CAAA;YACJ,CAAC;YAED,aAAa;YACb,MAAM,cAAc,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,CAAC;gBAC/C,GAAG,IAAI;gBACP,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;gBACvC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;gBACvC,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,WAAW,EAAE;aACvC,CAAC,CAAC,CAAA;YAEH,GAAG,CAAC,IAAI,CAAC;gBACP,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,cAAc;aACrB,CAAC,CAAA;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAA;YAClC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,SAAS;aACjB,CAAC,CAAA;QACJ,CAAC;IACH,CAAC,CAAC,CAAA;IAEF;;OAEG;IACH,GAAG,CAAC,GAAG,CAAC,GAAG,MAAM,YAAY,EAAE,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;QACnE,IAAI,CAAC;YACH,MAAM,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,CAAA;YAEzB,MAAM,IAAI,GAAG,MAAM,eAAe,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAA;YAE/C,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC1B,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,UAAU;iBAClB,CAAC,CAAA;YACJ,CAAC;YAED,SAAS;YACT,MAAM,aAAa,GAAG;gBACpB,GAAG,IAAI;gBACP,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;gBACvC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;gBACvC,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,WAAW,EAAE;aACvC,CAAA;YAED,GAAG,CAAC,IAAI,CAAC;gBACP,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,aAAa;aACpB,CAAC,CAAA;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAA;YAClC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,SAAS;aACjB,CAAC,CAAA;QACJ,CAAC;IACH,CAAC,CAAC,CAAA;IAEF;;OAEG;IACH,GAAG,CAAC,GAAG,CAAC,GAAG,MAAM,YAAY,EAAE,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;QACnE,IAAI,CAAC;YACH,MAAM,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,CAAA;YACzB,MAAM,OAAO,GAAG,GAAG,CAAC,IAAI,CAAA;YAExB,WAAW;YACX,MAAM,YAAY,GAAG,MAAM,eAAe,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAA;YACvD,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC1B,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,UAAU;iBAClB,CAAC,CAAA;YACJ,CAAC;YAED,SAAS;YACT,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;gBACrB,IAAI,CAAC;oBACH,OAAO,CAAC,QAAQ,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;oBAC7C,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC;wBACtC,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;4BAC1B,OAAO,EAAE,KAAK;4BACd,KAAK,EAAE,UAAU;yBAClB,CAAC,CAAA;oBACJ,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;wBAC1B,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,UAAU;qBAClB,CAAC,CAAA;gBACJ,CAAC;YACH,CAAC;YAED,OAAO;YACP,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;gBACrB,MAAM,eAAe,GAAG,CAAC,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC,CAAA;gBAC7E,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAChD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;wBAC1B,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,OAAO;qBACf,CAAC,CAAA;gBACJ,CAAC;YACH,CAAC;YAED,OAAO;YACP,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;gBACnB,MAAM,aAAa,GAAG,CAAC,UAAU,EAAE,YAAY,EAAE,WAAW,EAAE,UAAU,CAAC,CAAA;gBACzE,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC5C,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;wBAC1B,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,OAAO;qBACf,CAAC,CAAA;gBACJ,CAAC;YACH,CAAC;YAED,MAAM,eAAe,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,CAAC,CAAA;YAEzC,GAAG,CAAC,IAAI,CAAC;gBACP,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,MAAM;aAChB,CAAC,CAAA;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAA;YAClC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,SAAS;aACjB,CAAC,CAAA;QACJ,CAAC;IACH,CAAC,CAAC,CAAA;IAEF;;OAEG;IACH,GAAG,CAAC,MAAM,CAAC,GAAG,MAAM,YAAY,EAAE,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;QACtE,IAAI,CAAC;YACH,MAAM,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,CAAA;YAEzB,WAAW;YACX,MAAM,YAAY,GAAG,MAAM,eAAe,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAA;YACvD,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC1B,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,UAAU;iBAClB,CAAC,CAAA;YACJ,CAAC;YAED,MAAM,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC,CAAA;YAEhC,GAAG,CAAC,IAAI,CAAC;gBACP,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,MAAM;aAChB,CAAC,CAAA;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAA;YAClC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,SAAS;aACjB,CAAC,CAAA;QACJ,CAAC;IACH,CAAC,CAAC,CAAA;AAGJ,CAAC"}