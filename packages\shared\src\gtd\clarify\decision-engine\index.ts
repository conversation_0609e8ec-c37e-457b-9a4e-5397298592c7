// 决策引擎模块导出

export * from './decision-context';
export * from './decision-tree';

// 规则导出
export * from './rules/actionability-rule';
export * from './rules/time-rule';
export * from './rules/ownership-rule';
export * from './rules/complexity-rule';
export * from './rules/calendar-rule';

import { DecisionTree } from './decision-tree';
import { ActionabilityRule } from './rules/actionability-rule';
import { TimeRule } from './rules/time-rule';
import { OwnershipRule } from './rules/ownership-rule';
import { ComplexityRule } from './rules/complexity-rule';
import { CalendarRule } from './rules/calendar-rule';

/**
 * 决策引擎工厂类
 * 提供便捷的决策引擎创建和配置方法
 */
export class DecisionEngineFactory {
  /**
   * 创建标准的GTD决策引擎
   * 包含所有核心决策规则
   */
  static createStandardEngine(logger?: (level: string, message: string, data?: any) => void): DecisionTree {
    const engine = new DecisionTree(logger);
    
    // 添加核心决策规则（按GTD流程顺序）
    engine.addRules([
      new ActionabilityRule(),  // 1. 可行动性检查
      new TimeRule(),          // 2. 时间判断 (2分钟规则)
      new OwnershipRule(),     // 3. 责任归属判断
      new ComplexityRule(),    // 4. 复杂度判断 (单一行动 vs 项目)
      new CalendarRule()       // 5. 日历判断
    ]);
    
    return engine;
  }

  /**
   * 创建自定义决策引擎
   * 允许指定特定的规则集合
   */
  static createCustomEngine(
    rules: Array<new() => any>,
    logger?: (level: string, message: string, data?: any) => void
  ): DecisionTree {
    const engine = new DecisionTree(logger);
    
    // 实例化并添加规则
    const ruleInstances = rules.map(RuleClass => new RuleClass());
    engine.addRules(ruleInstances);
    
    return engine;
  }

  /**
   * 创建最小化决策引擎
   * 只包含最基本的决策规则
   */
  static createMinimalEngine(logger?: (level: string, message: string, data?: any) => void): DecisionTree {
    const engine = new DecisionTree(logger);
    
    // 只添加最基本的规则
    engine.addRules([
      new ActionabilityRule(),
      new TimeRule()
    ]);
    
    return engine;
  }

  /**
   * 获取所有可用的规则类
   */
  static getAvailableRules(): Array<new() => any> {
    return [
      ActionabilityRule,
      TimeRule,
      OwnershipRule,
      ComplexityRule,
      CalendarRule
    ];
  }

  /**
   * 获取规则信息
   */
  static getRuleInfo(): Array<{name: string, priority: number, description: string}> {
    return [
      {
        name: 'ActionabilityRule',
        priority: 1,
        description: '判断事项是否可行动，区分行动项目和非行动项目'
      },
      {
        name: 'TimeRule', 
        priority: 2,
        description: '应用2分钟规则，识别快速行动'
      },
      {
        name: 'OwnershipRule',
        priority: 3,
        description: '判断责任归属，识别需要委派的事项'
      },
      {
        name: 'ComplexityRule',
        priority: 4,
        description: '判断复杂度，区分单一行动和多步骤项目'
      },
      {
        name: 'CalendarRule',
        priority: 5,
        description: '识别有特定时间要求的事项'
      }
    ];
  }
}
