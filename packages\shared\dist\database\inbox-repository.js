"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SQLiteInboxRepository = void 0;
const uuid_1 = require("uuid");
/**
 * SQLite 收集箱仓储实现
 */
class SQLiteInboxRepository {
    connection;
    constructor(connection) {
        this.connection = connection;
    }
    /**
     * 创建新条目
     */
    async create(item) {
        const id = (0, uuid_1.v4)();
        const now = new Date();
        const newItem = {
            id,
            description: item.description || '',
            expectedOutcome: item.expectedOutcome,
            deadline: item.deadline,
            estimatedMinutes: item.estimatedMinutes,
            category: item.category,
            source: item.source || 'manual',
            status: item.status || 'captured',
            rawInput: item.rawInput,
            aiSuggestions: item.aiSuggestions,
            createdAt: now,
            updatedAt: now,
            version: 1
        };
        await this.save(newItem);
        return newItem;
    }
    /**
     * 保存收集箱条目
     */
    async save(item) {
        const id = item.id || (0, uuid_1.v4)();
        const now = new Date().toISOString();
        const sql = `
      INSERT INTO inbox_items (
        id, description, expected_outcome, deadline, estimated_minutes,
        category, source, status, raw_input, ai_suggestions,
        created_at, updated_at, version
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
        const params = [
            id,
            item.description,
            item.expectedOutcome || null,
            item.deadline?.toISOString() || null,
            item.estimatedMinutes || null,
            item.category || null,
            item.source,
            item.status,
            item.rawInput || null,
            item.aiSuggestions ? JSON.stringify(item.aiSuggestions) : null,
            item.createdAt?.toISOString() || now,
            item.updatedAt?.toISOString() || now,
            item.version || 1
        ];
        await this.connection.run(sql, params);
        return id;
    }
    /**
     * 根据ID查找条目
     */
    async findById(id) {
        const sql = 'SELECT * FROM inbox_items WHERE id = ?';
        const record = await this.connection.get(sql, [id]);
        return record ? this.mapRecordToItem(record) : null;
    }
    /**
     * 查找所有条目
     */
    async findAll(options = {}) {
        const { limit = 100, offset = 0, orderBy = 'created_at', orderDirection = 'DESC' } = options;
        const sql = `
      SELECT * FROM inbox_items 
      ORDER BY ${orderBy} ${orderDirection}
      LIMIT ? OFFSET ?
    `;
        const records = await this.connection.all(sql, [limit, offset]);
        return records.map(record => this.mapRecordToItem(record));
    }
    /**
     * 更新条目
     */
    async update(id, updates) {
        const fields = [];
        const params = [];
        // 构建更新字段
        if (updates.description !== undefined) {
            fields.push('description = ?');
            params.push(updates.description);
        }
        if (updates.expectedOutcome !== undefined) {
            fields.push('expected_outcome = ?');
            params.push(updates.expectedOutcome);
        }
        if (updates.deadline !== undefined) {
            fields.push('deadline = ?');
            params.push(updates.deadline?.toISOString() || null);
        }
        if (updates.estimatedMinutes !== undefined) {
            fields.push('estimated_minutes = ?');
            params.push(updates.estimatedMinutes);
        }
        if (updates.category !== undefined) {
            fields.push('category = ?');
            params.push(updates.category);
        }
        if (updates.status !== undefined) {
            fields.push('status = ?');
            params.push(updates.status);
        }
        if (updates.aiSuggestions !== undefined) {
            fields.push('ai_suggestions = ?');
            params.push(updates.aiSuggestions ? JSON.stringify(updates.aiSuggestions) : null);
        }
        if (fields.length === 0) {
            // 如果没有更新字段，返回原始条目
            const existing = await this.findById(id);
            if (!existing) {
                throw new Error(`收集箱条目不存在: ${id}`);
            }
            return existing;
        }
        // 更新时间会通过触发器自动更新
        const sql = `UPDATE inbox_items SET ${fields.join(', ')} WHERE id = ?`;
        params.push(id);
        await this.connection.run(sql, params);
        // 返回更新后的条目
        const updated = await this.findById(id);
        if (!updated) {
            throw new Error(`更新后无法找到条目: ${id}`);
        }
        return updated;
    }
    /**
     * 删除条目
     */
    async delete(id) {
        const sql = 'DELETE FROM inbox_items WHERE id = ?';
        await this.connection.run(sql, [id]);
    }
    /**
     * 根据状态查找条目
     */
    async findByStatus(status, options = {}) {
        const { limit = 100, offset = 0, orderBy = 'created_at', orderDirection = 'DESC' } = options;
        const sql = `
      SELECT * FROM inbox_items 
      WHERE status = ?
      ORDER BY ${orderBy} ${orderDirection}
      LIMIT ? OFFSET ?
    `;
        const records = await this.connection.all(sql, [status, limit, offset]);
        return records.map(record => this.mapRecordToItem(record));
    }
    /**
     * 根据过滤器查找条目
     */
    async findByFilter(filter, options = {}) {
        const { whereClause, params } = this.buildWhereClause(filter);
        const { limit = 100, offset = 0, orderBy = 'created_at', orderDirection = 'DESC' } = options;
        const sql = `
      SELECT * FROM inbox_items 
      ${whereClause}
      ORDER BY ${orderBy} ${orderDirection}
      LIMIT ? OFFSET ?
    `;
        params.push(limit, offset);
        const records = await this.connection.all(sql, params);
        return records.map(record => this.mapRecordToItem(record));
    }
    /**
     * 统计条目数量
     */
    async count(filter) {
        if (!filter) {
            const result = await this.connection.get('SELECT COUNT(*) as count FROM inbox_items');
            return result?.count || 0;
        }
        const { whereClause, params } = this.buildWhereClause(filter);
        const sql = `SELECT COUNT(*) as count FROM inbox_items ${whereClause}`;
        const result = await this.connection.get(sql, params);
        return result?.count || 0;
    }
    /**
     * 批量保存条目
     */
    async saveMany(items) {
        const ids = [];
        await this.connection.beginTransaction();
        try {
            for (const item of items) {
                const id = await this.save(item);
                ids.push(id);
            }
            await this.connection.commit();
        }
        catch (error) {
            await this.connection.rollback();
            throw error;
        }
        return ids;
    }
    /**
     * 批量更新条目
     */
    async updateMany(updates) {
        await this.connection.beginTransaction();
        try {
            for (const { id, data } of updates) {
                await this.update(id, data);
            }
            await this.connection.commit();
        }
        catch (error) {
            await this.connection.rollback();
            throw error;
        }
    }
    /**
     * 批量删除条目
     */
    async deleteMany(ids) {
        if (ids.length === 0)
            return;
        const placeholders = ids.map(() => '?').join(',');
        const sql = `DELETE FROM inbox_items WHERE id IN (${placeholders})`;
        await this.connection.run(sql, ids);
    }
    /**
     * 初始化仓储
     */
    async initialize() {
        // 数据库表已通过迁移创建，这里可以做额外的初始化工作
    }
    /**
     * 关闭仓储
     */
    async close() {
        await this.connection.close();
    }
    /**
     * 数据库清理
     */
    async vacuum() {
        await this.connection.run('VACUUM');
    }
    /**
     * 将数据库记录映射为领域对象
     */
    mapRecordToItem(record) {
        return {
            id: record.id,
            description: record.description,
            expectedOutcome: record.expected_outcome || undefined,
            deadline: record.deadline ? new Date(record.deadline) : undefined,
            estimatedMinutes: record.estimated_minutes || undefined,
            category: record.category || undefined,
            source: record.source,
            status: record.status,
            rawInput: record.raw_input || undefined,
            aiSuggestions: record.ai_suggestions ? JSON.parse(record.ai_suggestions) : undefined,
            createdAt: new Date(record.created_at),
            updatedAt: new Date(record.updated_at),
            version: record.version
        };
    }
    /**
     * 构建 WHERE 子句
     */
    buildWhereClause(filter) {
        const conditions = [];
        const params = [];
        if (filter.status) {
            conditions.push('status = ?');
            params.push(filter.status);
        }
        if (filter.source) {
            conditions.push('source = ?');
            params.push(filter.source);
        }
        if (filter.category) {
            conditions.push('category = ?');
            params.push(filter.category);
        }
        if (filter.createdAfter) {
            conditions.push('created_at >= ?');
            params.push(filter.createdAfter.toISOString());
        }
        if (filter.createdBefore) {
            conditions.push('created_at <= ?');
            params.push(filter.createdBefore.toISOString());
        }
        if (filter.hasDeadline !== undefined) {
            if (filter.hasDeadline) {
                conditions.push('deadline IS NOT NULL');
            }
            else {
                conditions.push('deadline IS NULL');
            }
        }
        const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
        return { whereClause, params };
    }
}
exports.SQLiteInboxRepository = SQLiteInboxRepository;
//# sourceMappingURL=inbox-repository.js.map