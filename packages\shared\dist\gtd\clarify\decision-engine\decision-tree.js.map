{"version": 3, "file": "decision-tree.js", "sourceRoot": "", "sources": ["../../../../src/gtd/clarify/decision-engine/decision-tree.ts"], "names": [], "mappings": ";AAAA,wBAAwB;;;AAGxB,yDAAqD;AAErD,4CAAiF;AAEjF;;;GAGG;AACH,MAAa,YAAY;IACf,KAAK,GAAmB,EAAE,CAAC;IAC3B,MAAM,CAAwD;IAEtE,YAAY,MAA6D;QACvE,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED,eAAe;IAEf;;OAEG;IACH,OAAO,CAAC,IAAkB;QACxB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtB,SAAS;QACT,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;QACnD,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,WAAW,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;IACzE,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,KAAqB;QAC5B,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,QAAgB;QACzB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;QACnE,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACjB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAC5B,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,WAAW,QAAQ,EAAE,CAAC,CAAC;YACzC,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;IACzB,CAAC;IAED,eAAe;IAEf;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,IAAe,EAAE,OAAmC;QACpE,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,WAAW,EAAE;gBAC5B,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,WAAW,EAAE,IAAI,CAAC,WAAW;aAC9B,CAAC,CAAC;YAEH,UAAU;YACV,MAAM,eAAe,GAAG,IAAI,kCAAe,CACzC,IAAI,EACJ,OAAO,EAAE,eAAe,EACxB,OAAO,EAAE,eAAe,EACxB,OAAO,EAAE,WAAW,CACrB,CAAC;YAEF,SAAS;YACT,MAAM,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC;YAEzC,SAAS;YACT,MAAM,QAAQ,GAAG,eAAe,CAAC,WAAW,EAAE,CAAC;YAE/C,SAAS;YACT,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YAEhC,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC9C,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE;gBACzB,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,cAAc;aACf,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC;QAElB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC9C,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE;gBAC1B,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC7D,cAAc;aACf,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAChB,KAAkB,EAClB,OAAmC;QAEnC,MAAM,OAAO,GAAsB,EAAE,CAAC;QACtC,MAAM,MAAM,GAA6C,EAAE,CAAC;QAE5D,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;QAExD,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;gBACvD,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACzB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAC5E,MAAM,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC,CAAC;gBACtD,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,YAAY,EAAE;oBAC9B,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,KAAK,EAAE,YAAY;iBACpB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE;YACzB,KAAK,EAAE,KAAK,CAAC,MAAM;YACnB,UAAU,EAAE,OAAO,CAAC,MAAM;YAC1B,MAAM,EAAE,MAAM,CAAC,MAAM;SACtB,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,eAAe;IAEf;;OAEG;IACK,KAAK,CAAC,YAAY,CAAC,OAAwB;QACjD,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,UAAU,EAAE;YAC5B,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM;YAC5B,MAAM,EAAE,OAAO,CAAC,OAAO,EAAE,CAAC,EAAE;SAC7B,CAAC,CAAC;QAEH,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YAC9B,IAAI,CAAC;gBACH,aAAa;gBACb,IAAI,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;oBACjC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,cAAc,EAAE;wBAChC,WAAW,EAAE,IAAI,CAAC,IAAI;qBACvB,CAAC,CAAC;oBACH,MAAM;gBACR,CAAC;gBAED,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE,EAAE;oBACtC,QAAQ,EAAE,IAAI,CAAC,QAAQ;iBACxB,CAAC,CAAC;gBAEH,OAAO;gBACP,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;gBAEhD,SAAS;gBACT,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;gBAE5C,SAAS;gBACT,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAElC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,WAAW,IAAI,CAAC,IAAI,EAAE,EAAE;oBACxC,UAAU,EAAE,UAAU,CAAC,UAAU;oBACjC,cAAc,EAAE,UAAU,CAAC,cAAc;iBAC1C,CAAC,CAAC;gBAEH,iBAAiB;gBACjB,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC;oBAC/B,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,cAAc,EAAE;wBAChC,SAAS,EAAE,IAAI,CAAC,IAAI;qBACrB,CAAC,CAAC;oBACH,MAAM;gBACR,CAAC;YAEH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAC5E,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,WAAW,IAAI,CAAC,IAAI,EAAE,EAAE;oBACxC,KAAK,EAAE,YAAY;iBACpB,CAAC,CAAC;gBAEH,oBAAoB;gBACpB,OAAO,CAAC,YAAY,CAAC,MAAM,IAAI,CAAC,IAAI,UAAU,YAAY,EAAE,CAAC,CAAC;YAChE,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,OAAwB,EAAE,MAAkB;QACpE,SAAS;QACT,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;YACpB,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;YAEjC,IAAI,QAAQ,CAAC,YAAY,KAAK,SAAS,EAAE,CAAC;gBACxC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YAC/C,CAAC;YACD,IAAI,QAAQ,CAAC,iBAAiB,KAAK,SAAS,EAAE,CAAC;gBAC7C,OAAO,CAAC,oBAAoB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC;YAC3D,CAAC;YACD,IAAI,QAAQ,CAAC,kBAAkB,KAAK,SAAS,EAAE,CAAC;gBAC9C,OAAO,CAAC,mBAAmB,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;YAC3D,CAAC;YACD,IAAI,QAAQ,CAAC,cAAc,KAAK,SAAS,EAAE,CAAC;gBAC1C,OAAO,CAAC,eAAe,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;YACnD,CAAC;YACD,IAAI,QAAQ,CAAC,eAAe,KAAK,SAAS,EAAE,CAAC;gBAC3C,OAAO,CAAC,kBAAkB,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;YACvD,CAAC;YACD,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC;gBACxB,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;YAC7C,CAAC;YACD,IAAI,QAAQ,CAAC,qBAAqB,KAAK,SAAS,EAAE,CAAC;gBACjD,OAAO,CAAC,wBAAwB,CAAC,QAAQ,CAAC,qBAAqB,CAAC,CAAC;YACnE,CAAC;QACH,CAAC;QAED,SAAS;QACT,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;QAEjE,QAAQ;QACR,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,QAAyB;QAChD,SAAS;QACT,IAAI,QAAQ,CAAC,YAAY,KAAK,SAAS,EAAE,CAAC;YACxC,MAAM,IAAI,KAAK,CAAC,GAAG,0BAAc,CAAC,qBAAqB,YAAY,CAAC,CAAC;QACvE,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,GAAG,0BAAc,CAAC,qBAAqB,UAAU,CAAC,CAAC;QACrE,CAAC;QAED,QAAQ;QACR,IAAI,QAAQ,CAAC,UAAU,GAAG,iCAAqB,CAAC,OAAO,EAAE,CAAC;YACxD,MAAM,IAAI,KAAK,CAAC,GAAG,0BAAc,CAAC,uBAAuB,KAAK,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;QACvF,CAAC;QAED,YAAY;QACZ,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,QAAyB;QACrD,mBAAmB;QACnB,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC;YAC3B,IAAI,QAAQ,CAAC,iBAAiB,KAAK,SAAS;gBACxC,QAAQ,CAAC,kBAAkB,KAAK,SAAS;gBACzC,QAAQ,CAAC,cAAc,KAAK,SAAS;gBACrC,QAAQ,CAAC,eAAe,KAAK,SAAS,EAAE,CAAC;gBAC3C,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,eAAe,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;YAClD,CAAC;QACH,CAAC;QAED,aAAa;QACb,IAAI,QAAQ,CAAC,iBAAiB,IAAI,QAAQ,CAAC,UAAU,KAAK,UAAU,EAAE,CAAC;YACrE,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,oBAAoB,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;QACvD,CAAC;QAED,SAAS;QACT,IAAI,QAAQ,CAAC,kBAAkB,KAAK,KAAK,IAAI,QAAQ,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;YAC/E,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,iBAAiB,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,GAAG,CAAC,KAAa,EAAE,OAAe,EAAE,IAAU;QACpD,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QACpC,CAAC;IACH,CAAC;CACF;AA9RD,oCA8RC"}