"use strict";
// GTD 明晰模块主入口
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClarifyStatsCollector = exports.ClarifyHealthChecker = exports.ClarifyModuleFactory = exports.DEFAULT_CONFIG = exports.TIME_CONSTANTS = exports.CONFIDENCE_THRESHOLDS = exports.RULE_PRIORITIES = exports.DecisionEngineFactory = exports.ClarifyService = exports.ClarifyWizard = exports.ClarifyServiceMVP = void 0;
// MVP版本：用户主导的决策流程
var clarify_service_mvp_1 = require("./clarify-service-mvp");
Object.defineProperty(exports, "ClarifyServiceMVP", { enumerable: true, get: function () { return clarify_service_mvp_1.ClarifyServiceMVP; } });
var clarify_wizard_1 = require("./wizard/clarify-wizard");
Object.defineProperty(exports, "ClarifyWizard", { enumerable: true, get: function () { return clarify_wizard_1.ClarifyWizard; } });
// Pro版本：AI辅助的决策流程
var clarify_service_1 = require("./clarify-service");
Object.defineProperty(exports, "ClarifyService", { enumerable: true, get: function () { return clarify_service_1.ClarifyService; } });
var decision_engine_1 = require("./decision-engine");
Object.defineProperty(exports, "DecisionEngineFactory", { enumerable: true, get: function () { return decision_engine_1.DecisionEngineFactory; } });
var constants_1 = require("./constants");
Object.defineProperty(exports, "RULE_PRIORITIES", { enumerable: true, get: function () { return constants_1.RULE_PRIORITIES; } });
Object.defineProperty(exports, "CONFIDENCE_THRESHOLDS", { enumerable: true, get: function () { return constants_1.CONFIDENCE_THRESHOLDS; } });
Object.defineProperty(exports, "TIME_CONSTANTS", { enumerable: true, get: function () { return constants_1.TIME_CONSTANTS; } });
Object.defineProperty(exports, "DEFAULT_CONFIG", { enumerable: true, get: function () { return constants_1.DEFAULT_CONFIG; } });
// 便捷导入
const clarify_service_mvp_2 = require("./clarify-service-mvp");
const clarify_service_2 = require("./clarify-service");
const decision_engine_2 = require("./decision-engine");
/**
 * 明晰模块工厂类
 * 提供便捷的服务创建方法，支持MVP和Pro两个版本
 */
class ClarifyModuleFactory {
    /**
     * 创建MVP版本明晰服务（用户主导决策）
     * 推荐用于初期产品和用户学习GTD流程
     */
    static createMVPService(inboxRepository, taskRepository, projectRepository, logger) {
        return new clarify_service_mvp_2.ClarifyServiceMVP(inboxRepository, taskRepository, projectRepository, logger);
    }
    /**
     * 创建Pro版本明晰服务（AI辅助决策）
     * 包含完整的GTD决策引擎，适合高级用户
     */
    static createProService(inboxRepository, taskRepository, projectRepository, logger) {
        const decisionEngine = decision_engine_2.DecisionEngineFactory.createStandardEngine(logger);
        return new clarify_service_2.ClarifyService(inboxRepository, taskRepository, projectRepository, decisionEngine, logger);
    }
    /**
     * 创建标准明晰服务（向后兼容）
     * 默认创建Pro版本
     */
    static createClarifyService(inboxRepository, taskRepository, projectRepository, logger) {
        return this.createProService(inboxRepository, taskRepository, projectRepository, logger);
    }
    /**
     * 创建自定义Pro版本明晰服务
     * 允许指定自定义的决策引擎
     */
    static createCustomProService(inboxRepository, taskRepository, projectRepository, decisionEngine, logger) {
        return new clarify_service_2.ClarifyService(inboxRepository, taskRepository, projectRepository, decisionEngine, logger);
    }
    /**
     * 获取推荐的服务版本
     * 根据用户经验水平推荐合适的版本
     */
    static getRecommendedService(userExperience, inboxRepository, taskRepository, projectRepository, logger) {
        switch (userExperience) {
            case 'beginner':
            case 'intermediate':
                return this.createMVPService(inboxRepository, taskRepository, projectRepository, logger);
            case 'advanced':
                return this.createProService(inboxRepository, taskRepository, projectRepository, logger);
            default:
                return this.createMVPService(inboxRepository, taskRepository, projectRepository, logger);
        }
    }
    /**
     * 创建最小化Pro版本明晰服务
     * 只包含基本的决策规则
     */
    static createMinimalProService(inboxRepository, taskRepository, projectRepository, logger) {
        const decisionEngine = decision_engine_2.DecisionEngineFactory.createMinimalEngine(logger);
        return new clarify_service_2.ClarifyService(inboxRepository, taskRepository, projectRepository, decisionEngine, logger);
    }
    /**
     * 获取支持的目标清单类型
     */
    static getSupportedTargetLists() {
        return [
            'trash',
            'reference',
            'incubator',
            'next-actions',
            'waiting',
            'projects',
            'calendar'
        ];
    }
    /**
     * 获取决策规则信息
     */
    static getDecisionRulesInfo() {
        return decision_engine_2.DecisionEngineFactory.getRuleInfo();
    }
    /**
     * 验证明晰服务配置
     */
    static validateConfiguration(config) {
        const { inboxRepository, taskRepository, projectRepository } = config;
        // 检查必需的仓库方法
        const requiredMethods = ['findById', 'create', 'update'];
        for (const method of requiredMethods) {
            if (typeof inboxRepository[method] !== 'function') {
                throw new Error(`InboxRepository 缺少方法: ${method}`);
            }
            if (typeof taskRepository[method] !== 'function') {
                throw new Error(`TaskRepository 缺少方法: ${method}`);
            }
            if (typeof projectRepository[method] !== 'function') {
                throw new Error(`ProjectRepository 缺少方法: ${method}`);
            }
        }
        return true;
    }
}
exports.ClarifyModuleFactory = ClarifyModuleFactory;
/**
 * 明晰服务健康检查
 */
class ClarifyHealthChecker {
    clarifyService;
    constructor(clarifyService) {
        this.clarifyService = clarifyService;
    }
    /**
     * 执行健康检查
     */
    async healthCheck() {
        const checks = [];
        let overallStatus = 'healthy';
        // 检查决策引擎
        try {
            // 这里可以添加具体的健康检查逻辑
            checks.push({
                name: 'decision_engine',
                status: 'pass'
            });
        }
        catch (error) {
            checks.push({
                name: 'decision_engine',
                status: 'fail',
                message: error instanceof Error ? error.message : String(error)
            });
            overallStatus = 'unhealthy';
        }
        // 检查数据库连接
        try {
            // 这里可以添加数据库连接检查
            checks.push({
                name: 'database_connection',
                status: 'pass'
            });
        }
        catch (error) {
            checks.push({
                name: 'database_connection',
                status: 'fail',
                message: error instanceof Error ? error.message : String(error)
            });
            overallStatus = 'unhealthy';
        }
        return {
            status: overallStatus,
            checks
        };
    }
}
exports.ClarifyHealthChecker = ClarifyHealthChecker;
/**
 * 明晰统计信息收集器
 */
class ClarifyStatsCollector {
    stats = {
        totalClarified: 0,
        byTargetList: new Map(),
        averageProcessingTime: 0,
        totalProcessingTime: 0,
        errorCount: 0
    };
    /**
     * 记录明晰结果
     */
    recordClarification(result) {
        this.stats.totalClarified++;
        this.stats.totalProcessingTime += result.processingTime;
        this.stats.averageProcessingTime = this.stats.totalProcessingTime / this.stats.totalClarified;
        const targetList = result.decision.targetList;
        const currentCount = this.stats.byTargetList.get(targetList) || 0;
        this.stats.byTargetList.set(targetList, currentCount + 1);
    }
    /**
     * 记录错误
     */
    recordError() {
        this.stats.errorCount++;
    }
    /**
     * 获取统计信息
     */
    getStats() {
        return {
            ...this.stats,
            byTargetList: Object.fromEntries(this.stats.byTargetList),
            successRate: this.stats.totalClarified / (this.stats.totalClarified + this.stats.errorCount)
        };
    }
    /**
     * 重置统计信息
     */
    reset() {
        this.stats = {
            totalClarified: 0,
            byTargetList: new Map(),
            averageProcessingTime: 0,
            totalProcessingTime: 0,
            errorCount: 0
        };
    }
}
exports.ClarifyStatsCollector = ClarifyStatsCollector;
//# sourceMappingURL=index.js.map