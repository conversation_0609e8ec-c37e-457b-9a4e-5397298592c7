"use strict";
// GTD 模块主入口
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DEFAULTS = exports.CONTEXT_KEYWORDS = exports.CATEGORY_KEYWORDS = exports.PARSE_PATTERNS = exports.captureUtils = void 0;
// 导出捕获模块
__exportStar(require("./capture"), exports);
// 导出明晰模块
__exportStar(require("./clarify"), exports);
// 导出共享工具 (避免重复导出)
var shared_1 = require("./shared");
Object.defineProperty(exports, "captureUtils", { enumerable: true, get: function () { return shared_1.captureUtils; } });
Object.defineProperty(exports, "PARSE_PATTERNS", { enumerable: true, get: function () { return shared_1.PARSE_PATTERNS; } });
Object.defineProperty(exports, "CATEGORY_KEYWORDS", { enumerable: true, get: function () { return shared_1.CATEGORY_KEYWORDS; } });
Object.defineProperty(exports, "CONTEXT_KEYWORDS", { enumerable: true, get: function () { return shared_1.CONTEXT_KEYWORDS; } });
Object.defineProperty(exports, "DEFAULTS", { enumerable: true, get: function () { return shared_1.DEFAULTS; } });
// 未来模块导出 (占位)
// export * from './organize';
// export * from './reflect';
// export * from './engage';
//# sourceMappingURL=index.js.map