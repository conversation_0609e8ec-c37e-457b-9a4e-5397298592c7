"use strict";
// 复杂度规则 - 判断是单一行动还是多步骤项目
Object.defineProperty(exports, "__esModule", { value: true });
exports.ComplexityRule = void 0;
const constants_1 = require("../../constants");
/**
 * 复杂度规则
 * 判断任务是单一行动还是需要分解的多步骤项目
 */
class ComplexityRule {
    name = 'ComplexityRule';
    priority = constants_1.RULE_PRIORITIES.COMPLEXITY;
    async evaluate(context) {
        const item = context.getItem();
        // 只对可行动且由自己负责的事项应用复杂度规则
        const decision = context.getDecision();
        if (decision.isActionable === false || decision.isMyResponsibility === false) {
            return {
                ruleName: this.name,
                decision: {},
                confidence: constants_1.CONFIDENCE_THRESHOLDS.HIGH,
                reasoning: ['跳过复杂度规则：非行动事项或非自己负责'],
                shouldContinue: true
            };
        }
        // 分析任务复杂度
        const analysis = this.analyzeComplexity(item);
        // 判断是否为单一行动
        const isSingleAction = this.determineSingleAction(analysis, item);
        // 构建推理过程
        const reasoning = this.buildReasoning(analysis, isSingleAction);
        // 计算置信度
        const confidence = this.calculateConfidence(analysis);
        // 设置目标清单
        let targetList;
        if (isSingleAction) {
            targetList = 'next-actions';
        }
        else {
            targetList = 'projects';
        }
        return {
            ruleName: this.name,
            decision: {
                isSingleAction,
                targetList: targetList
            },
            confidence,
            reasoning,
            shouldContinue: true
        };
    }
    /**
     * 分析任务复杂度
     */
    analyzeComplexity(item) {
        const description = item.description.toLowerCase();
        const analysis = {
            hasProjectIndicators: false,
            hasOutcomeIndicators: false,
            hasMultiStepWords: false,
            estimatedMinutes: 0,
            projectKeywords: [],
            outcomeKeywords: [],
            multiStepWords: [],
            stepCount: 0,
            hasSubTasks: false
        };
        // 检查项目指示词
        for (const indicator of constants_1.PROJECT_KEYWORDS.PROJECT_INDICATORS) {
            if (description.includes(indicator)) {
                analysis.hasProjectIndicators = true;
                analysis.projectKeywords.push(indicator);
            }
        }
        // 检查成果指示词
        for (const indicator of constants_1.PROJECT_KEYWORDS.OUTCOME_INDICATORS) {
            if (description.includes(indicator)) {
                analysis.hasOutcomeIndicators = true;
                analysis.outcomeKeywords.push(indicator);
            }
        }
        // 检查多步骤词汇
        const multiStepWords = [
            '计划', '准备', '组织', '安排', '设计', '开发', '建立', '创建',
            '实施', '执行', '推进', '启动', '完善', '优化', '改进', '重构',
            '系统', '流程', '方案', '策略', '培训', '学习', '研究'
        ];
        for (const word of multiStepWords) {
            if (description.includes(word)) {
                analysis.hasMultiStepWords = true;
                analysis.multiStepWords.push(word);
            }
        }
        // 获取预估时间
        analysis.estimatedMinutes = item.estimatedMinutes || 0;
        // 检查是否有明确的步骤描述
        analysis.stepCount = this.countSteps(description);
        analysis.hasSubTasks = this.hasSubTaskIndicators(description);
        return analysis;
    }
    /**
     * 计算描述中的步骤数量
     */
    countSteps(description) {
        // 查找步骤指示器
        const stepPatterns = [
            /第[一二三四五六七八九十\d]+步/g,
            /步骤[一二三四五六七八九十\d]+/g,
            /\d+[、.]\s*/g,
            /[一二三四五六七八九十]+[、.]\s*/g
        ];
        let maxSteps = 0;
        for (const pattern of stepPatterns) {
            const matches = description.match(pattern);
            if (matches) {
                maxSteps = Math.max(maxSteps, matches.length);
            }
        }
        return maxSteps;
    }
    /**
     * 检查是否有子任务指示器
     */
    hasSubTaskIndicators(description) {
        const subTaskIndicators = [
            '包括', '涉及', '需要', '分为', '包含', '由', '组成',
            '首先', '然后', '接着', '最后', '同时', '另外'
        ];
        return subTaskIndicators.some(indicator => description.includes(indicator));
    }
    /**
     * 判断是否为单一行动
     */
    determineSingleAction(analysis, item) {
        // 强项目指示器
        if (analysis.hasProjectIndicators && analysis.hasOutcomeIndicators) {
            return false;
        }
        // 有明确的步骤描述
        if (analysis.stepCount >= 2) {
            return false;
        }
        // 有子任务指示器
        if (analysis.hasSubTasks) {
            return false;
        }
        // 时间过长，可能是项目
        if (analysis.estimatedMinutes > constants_1.TIME_CONSTANTS.DEFAULT_PROJECT_ESTIMATE) {
            return false;
        }
        // 有多步骤关键词
        if (analysis.hasMultiStepWords && analysis.multiStepWords.length >= 2) {
            return false;
        }
        // 检查是否为典型的单一行动
        if (this.isTypicalSingleAction(item.description)) {
            return true;
        }
        // 检查是否为典型的项目
        if (this.isTypicalProject(item.description)) {
            return false;
        }
        // 默认情况下，倾向于认为是单一行动
        return true;
    }
    /**
     * 检查是否为典型的单一行动
     */
    isTypicalSingleAction(description) {
        const text = description.toLowerCase();
        const singleActionIndicators = [
            '打电话', '发邮件', '发送', '回复', '转发', '下载', '上传',
            '购买', '预约', '确认', '检查', '查看', '阅读', '保存',
            '删除', '修改', '更新', '安装', '卸载', '备份', '恢复'
        ];
        return singleActionIndicators.some(indicator => text.includes(indicator));
    }
    /**
     * 检查是否为典型的项目
     */
    isTypicalProject(description) {
        const text = description.toLowerCase();
        const projectIndicators = [
            '开发', '设计', '建立', '创建', '实施', '推进', '完善',
            '优化', '改进', '重构', '培训', '学习', '研究', '调研',
            '策划', '规划', '组织', '管理', '协调', '整合'
        ];
        return projectIndicators.some(indicator => text.includes(indicator));
    }
    /**
     * 构建推理过程
     */
    buildReasoning(analysis, isSingleAction) {
        const reasoning = [];
        if (isSingleAction) {
            reasoning.push('判断为单一行动');
            reasoning.push('可以一次性完成，无需分解');
            if (analysis.estimatedMinutes > 0 && analysis.estimatedMinutes <= 60) {
                reasoning.push(`预估时间${analysis.estimatedMinutes}分钟，适合单次执行`);
            }
        }
        else {
            reasoning.push('判断为多步骤项目');
            reasoning.push('需要分解为多个具体行动');
            if (analysis.hasProjectIndicators) {
                reasoning.push(`检测到项目指示词: ${analysis.projectKeywords.join(', ')}`);
            }
            if (analysis.hasOutcomeIndicators) {
                reasoning.push(`检测到成果指示词: ${analysis.outcomeKeywords.join(', ')}`);
            }
            if (analysis.stepCount >= 2) {
                reasoning.push(`检测到${analysis.stepCount}个步骤`);
            }
            if (analysis.hasSubTasks) {
                reasoning.push('检测到子任务指示器');
            }
            if (analysis.estimatedMinutes > constants_1.TIME_CONSTANTS.DEFAULT_PROJECT_ESTIMATE) {
                reasoning.push(`预估时间${analysis.estimatedMinutes}分钟，超过项目阈值`);
            }
            reasoning.push('建议定义项目预期成果和下一步行动');
        }
        return reasoning;
    }
    /**
     * 计算置信度
     */
    calculateConfidence(analysis) {
        let confidence = constants_1.CONFIDENCE_THRESHOLDS.MEDIUM;
        // 有明确的项目指示器，提高置信度
        if (analysis.hasProjectIndicators && analysis.hasOutcomeIndicators) {
            confidence += 0.3;
        }
        // 有明确的步骤描述，提高置信度
        if (analysis.stepCount >= 2) {
            confidence += 0.2;
        }
        // 有预估时间，提高置信度
        if (analysis.estimatedMinutes > 0) {
            confidence += 0.1;
        }
        // 边界情况降低置信度
        if (analysis.estimatedMinutes > 60 && analysis.estimatedMinutes < 120) {
            confidence -= 0.1;
        }
        return Math.min(1.0, Math.max(constants_1.CONFIDENCE_THRESHOLDS.LOW, confidence));
    }
}
exports.ComplexityRule = ComplexityRule;
//# sourceMappingURL=complexity-rule.js.map