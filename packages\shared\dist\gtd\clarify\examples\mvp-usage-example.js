"use strict";
// GTD 明晰服务 MVP版本使用示例
Object.defineProperty(exports, "__esModule", { value: true });
exports.MVPUsageExample = void 0;
exports.runMVPExamples = runMVPExamples;
/**
 * MVP版本使用示例
 * 展示用户主导的逐步决策流程
 */
class MVPUsageExample {
    clarifyService;
    constructor(clarifyService) {
        this.clarifyService = clarifyService;
    }
    /**
     * 示例：明晰 "让小王帮忙整理下周会议的资料"
     */
    async demonstrateUserGuidedClarification() {
        console.log('=== GTD 明晰向导 MVP版本示例 ===\n');
        // 模拟收集箱条目
        const mockItem = {
            id: 'item-123',
            description: '让小王帮忙整理下周会议的资料',
            source: 'manual',
            status: 'captured',
            createdAt: new Date(),
            updatedAt: new Date(),
            version: 1
        };
        try {
            // 第1步：开始明晰流程
            console.log('📝 收集箱条目:', mockItem.description);
            const session = await this.clarifyService.startClarification(mockItem.id);
            console.log('✅ 明晰会话已创建:', session.id);
            console.log('');
            // 第2步：获取第一个问题
            let question = this.clarifyService.getCurrentQuestion(session.id);
            console.log('❓ 第1步问题:', question?.title);
            console.log('📖 说明:', question?.description);
            console.log('💡 帮助:', question?.helpText);
            console.log('选项:');
            question?.options.forEach((option, index) => {
                console.log(`  ${index + 1}. ${option.label} - ${option.description}`);
            });
            console.log('');
            // 用户选择：是，需要行动
            console.log('👤 用户选择: 1. 是，需要行动');
            await this.clarifyService.submitAnswer(session.id, true);
            console.log('');
            // 第3步：获取第二个问题
            question = this.clarifyService.getCurrentQuestion(session.id);
            console.log('❓ 第2步问题:', question?.title);
            console.log('选项:');
            question?.options.forEach((option, index) => {
                console.log(`  ${index + 1}. ${option.label} - ${option.description}`);
            });
            console.log('');
            // 用户选择：不是，需要更多时间
            console.log('👤 用户选择: 2. 不是，需要更多时间');
            await this.clarifyService.submitAnswer(session.id, false);
            console.log('');
            // 第4步：获取第三个问题
            question = this.clarifyService.getCurrentQuestion(session.id);
            console.log('❓ 第3步问题:', question?.title);
            console.log('选项:');
            question?.options.forEach((option, index) => {
                console.log(`  ${index + 1}. ${option.label} - ${option.description}`);
            });
            console.log('');
            // 用户选择：不是，需要委派
            console.log('👤 用户选择: 2. 不是，需要委派');
            console.log('👤 委派给: 小王');
            await this.clarifyService.submitAnswer(session.id, false, {
                delegatedTo: '小王'
            });
            console.log('');
            // 第5步：完成明晰
            console.log('🎯 明晰流程完成！');
            const result = await this.clarifyService.completeClarification(session.id);
            console.log('📊 明晰结果:');
            console.log('  目标清单:', result.targetList);
            console.log('  决策摘要:', result.summary);
            console.log('  创建条目数:', result.createdItems.length);
            console.log('  收集箱状态:', result.updatedInboxItem.status);
            console.log('');
            return result;
        }
        catch (error) {
            console.error('❌ 明晰过程出错:', error);
            throw error;
        }
    }
    /**
     * 示例：明晰 "写下周的工作报告"
     */
    async demonstrateTaskClarification() {
        console.log('=== 示例2：任务明晰 ===\n');
        const mockItem = {
            id: 'item-456',
            description: '写下周的工作报告',
            estimatedMinutes: 60,
            source: 'manual',
            status: 'captured',
            createdAt: new Date(),
            updatedAt: new Date(),
            version: 1
        };
        const session = await this.clarifyService.startClarification(mockItem.id);
        console.log('📝 收集箱条目:', mockItem.description);
        console.log('');
        // 模拟用户选择流程
        console.log('👤 第1步选择: 是，需要行动');
        await this.clarifyService.submitAnswer(session.id, true);
        console.log('👤 第2步选择: 不是，需要更多时间（预估60分钟）');
        await this.clarifyService.submitAnswer(session.id, false);
        console.log('👤 第3步选择: 是，我来做');
        await this.clarifyService.submitAnswer(session.id, true);
        console.log('👤 第4步选择: 是，单一行动');
        await this.clarifyService.submitAnswer(session.id, true);
        console.log('👤 第5步选择: 不是，时间灵活');
        await this.clarifyService.submitAnswer(session.id, false);
        const result = await this.clarifyService.completeClarification(session.id);
        console.log('🎯 结果: 放入执行清单');
        console.log('📊 决策摘要:', result.summary);
        console.log('');
        return result;
    }
    /**
     * 示例：明晰 "组织公司年会"
     */
    async demonstrateProjectClarification() {
        console.log('=== 示例3：项目明晰 ===\n');
        const mockItem = {
            id: 'item-789',
            description: '组织公司年会',
            expectedOutcome: '成功举办一场难忘的公司年会',
            source: 'manual',
            status: 'captured',
            createdAt: new Date(),
            updatedAt: new Date(),
            version: 1
        };
        const session = await this.clarifyService.startClarification(mockItem.id);
        console.log('📝 收集箱条目:', mockItem.description);
        console.log('');
        // 模拟用户选择流程
        console.log('👤 第1步选择: 是，需要行动');
        await this.clarifyService.submitAnswer(session.id, true);
        console.log('👤 第2步选择: 不是，需要更多时间');
        await this.clarifyService.submitAnswer(session.id, false);
        console.log('👤 第3步选择: 是，我来做');
        await this.clarifyService.submitAnswer(session.id, true);
        console.log('👤 第4步选择: 不是，多步骤项目');
        console.log('👤 项目成果: 成功举办一场难忘的公司年会，提升团队凝聚力');
        await this.clarifyService.submitAnswer(session.id, false, {
            projectOutcome: '成功举办一场难忘的公司年会，提升团队凝聚力'
        });
        const result = await this.clarifyService.completeClarification(session.id);
        console.log('🎯 结果: 创建项目 + 下一步行动');
        console.log('📊 决策摘要:', result.summary);
        console.log('📋 创建了', result.createdItems.length, '个条目:');
        result.createdItems.forEach(item => {
            console.log(`  - ${item.type}: ${item.data.title || item.data.description}`);
        });
        console.log('');
        return result;
    }
    /**
     * 展示完整的向导界面模拟
     */
    async simulateWizardUI() {
        console.log('=== 向导界面模拟 ===\n');
        const mockItem = {
            id: 'item-ui-demo',
            description: '明天下午2点开会讨论项目进展',
            source: 'manual',
            status: 'captured',
            createdAt: new Date(),
            updatedAt: new Date(),
            version: 1
        };
        const session = await this.clarifyService.startClarification(mockItem.id);
        console.log('┌─────────────────────────────────────────┐');
        console.log('│           GTD 明晰向导                    │');
        console.log('├─────────────────────────────────────────┤');
        console.log(`│ 事项: ${mockItem.description.padEnd(30)} │`);
        console.log('└─────────────────────────────────────────┘');
        console.log('');
        let currentSession = session;
        let stepNumber = 1;
        while (!currentSession.isComplete) {
            const question = this.clarifyService.getCurrentQuestion(currentSession.id);
            if (!question)
                break;
            console.log(`📋 第${stepNumber}步: ${question.title}`);
            console.log(`💭 ${question.description}`);
            console.log('');
            if (question.examples) {
                console.log('💡 示例:');
                question.examples.forEach(example => {
                    console.log(`   ${example}`);
                });
                console.log('');
            }
            console.log('🔘 请选择:');
            question.options.forEach((option, index) => {
                console.log(`   ${index + 1}. ${option.label}`);
                console.log(`      ${option.description}`);
            });
            console.log('');
            // 模拟用户选择（这里硬编码选择路径）
            let userChoice;
            let additionalData = {};
            switch (stepNumber) {
                case 1: // 可行动性
                    userChoice = true;
                    console.log('👤 用户选择: 1. 是，需要行动');
                    break;
                case 2: // 时间
                    userChoice = false;
                    console.log('👤 用户选择: 2. 不是，需要更多时间');
                    break;
                case 3: // 责任归属
                    userChoice = true;
                    console.log('👤 用户选择: 1. 是，我来做');
                    break;
                case 4: // 复杂度
                    userChoice = true;
                    console.log('👤 用户选择: 1. 是，单一行动');
                    break;
                case 5: // 日历
                    userChoice = true;
                    additionalData.calendarTime = new Date('2024-01-15T14:00:00');
                    console.log('👤 用户选择: 1. 是，有特定时间');
                    console.log('👤 时间: 明天下午2点');
                    break;
            }
            currentSession = await this.clarifyService.submitAnswer(currentSession.id, userChoice, additionalData);
            console.log('✅ 已保存选择');
            console.log('');
            console.log('─'.repeat(50));
            console.log('');
            stepNumber++;
        }
        const result = await this.clarifyService.completeClarification(currentSession.id);
        console.log('🎉 明晰完成！');
        console.log('');
        console.log('📊 最终结果:');
        console.log(`   目标清单: ${result.targetList}`);
        console.log(`   决策路径: ${result.summary}`);
        console.log(`   创建条目: ${result.createdItems.length}个`);
        console.log('');
        return result;
    }
}
exports.MVPUsageExample = MVPUsageExample;
/**
 * 运行所有示例
 */
async function runMVPExamples(clarifyService) {
    const examples = new MVPUsageExample(clarifyService);
    try {
        await examples.demonstrateUserGuidedClarification();
        await examples.demonstrateTaskClarification();
        await examples.demonstrateProjectClarification();
        await examples.simulateWizardUI();
        console.log('✅ 所有示例运行完成！');
    }
    catch (error) {
        console.error('❌ 示例运行失败:', error);
    }
}
//# sourceMappingURL=mvp-usage-example.js.map