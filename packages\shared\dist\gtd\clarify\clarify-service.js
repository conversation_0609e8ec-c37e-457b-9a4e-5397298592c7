"use strict";
// GTD 明晰主服务 - 整合决策引擎和业务逻辑
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClarifyService = void 0;
const constants_1 = require("./constants");
/**
 * GTD 明晰服务
 * 负责协调整个明晰流程，从决策到实际创建目标条目
 */
class ClarifyService {
    inboxRepository;
    taskRepository;
    projectRepository;
    decisionEngine;
    logger;
    eventListeners = new Map();
    constructor(inboxRepository, taskRepository, projectRepository, decisionEngine, logger) {
        this.inboxRepository = inboxRepository;
        this.taskRepository = taskRepository;
        this.projectRepository = projectRepository;
        this.decisionEngine = decisionEngine;
        this.logger = logger;
    }
    // === 核心明晰方法 ===
    /**
     * 明晰单个收集箱条目
     */
    async clarifyItem(itemId) {
        const startTime = Date.now();
        try {
            this.log('info', '开始明晰处理', { itemId });
            this.emit(constants_1.EVENT_TYPES.CLARIFY_STARTED, { itemId });
            // 1. 获取收集箱条目
            const item = await this.inboxRepository.findById(itemId);
            if (!item) {
                throw new Error(`收集箱条目不存在: ${itemId}`);
            }
            // 2. 更新状态为处理中
            await this.inboxRepository.update(itemId, { status: 'processing' });
            // 3. 执行决策流程
            const decision = await this.decisionEngine.processItem(item);
            this.emit(constants_1.EVENT_TYPES.DECISION_MADE, { itemId, decision });
            // 4. 根据决策创建目标条目
            const createdItems = await this.createTargetItems(item, decision);
            // 5. 更新收集箱状态
            const updatedItem = await this.inboxRepository.update(itemId, {
                status: 'clarified'
            });
            const processingTime = Date.now() - startTime;
            const result = {
                decision,
                createdItems,
                updatedInboxItem: updatedItem,
                processingTime
            };
            this.log('info', '明晰处理完成', {
                itemId,
                targetList: decision.targetList,
                createdItemsCount: createdItems.length,
                processingTime
            });
            this.emit(constants_1.EVENT_TYPES.CLARIFY_COMPLETED, { itemId, result });
            return result;
        }
        catch (error) {
            const processingTime = Date.now() - startTime;
            const errorMessage = error instanceof Error ? error.message : String(error);
            this.log('error', '明晰处理失败', {
                itemId,
                error: errorMessage,
                processingTime
            });
            this.emit(constants_1.EVENT_TYPES.CLARIFY_FAILED, { itemId, error: errorMessage });
            // 恢复收集箱状态
            try {
                await this.inboxRepository.update(itemId, { status: 'captured' });
            }
            catch (updateError) {
                this.log('error', '恢复收集箱状态失败', { itemId, updateError });
            }
            throw error;
        }
    }
    /**
     * 批量明晰收集箱条目
     */
    async batchClarify(itemIds, options = {}) {
        const startTime = Date.now();
        const { maxConcurrent = 5, stopOnError = false, userInteraction = false } = options;
        this.log('info', '开始批量明晰', {
            itemCount: itemIds.length,
            options
        });
        const results = [];
        const errors = [];
        // 分批处理
        for (let i = 0; i < itemIds.length; i += maxConcurrent) {
            const batch = itemIds.slice(i, i + maxConcurrent);
            const batchPromises = batch.map(async (itemId) => {
                try {
                    const result = await this.clarifyItem(itemId);
                    results.push(result);
                }
                catch (error) {
                    const errorMessage = error instanceof Error ? error.message : String(error);
                    errors.push({ itemId, error: errorMessage });
                    if (stopOnError) {
                        throw error;
                    }
                }
            });
            try {
                await Promise.all(batchPromises);
            }
            catch (error) {
                if (stopOnError) {
                    break;
                }
            }
        }
        const processingTime = Date.now() - startTime;
        const batchResult = {
            results,
            errors,
            summary: {
                total: itemIds.length,
                successful: results.length,
                failed: errors.length,
                processingTime
            }
        };
        this.log('info', '批量明晰完成', batchResult.summary);
        return batchResult;
    }
    // === 目标条目创建 ===
    /**
     * 根据决策创建目标条目
     */
    async createTargetItems(item, decision) {
        const createdItems = [];
        switch (decision.targetList) {
            case 'next-actions':
                const task = await this.createTask(item, decision);
                createdItems.push({
                    type: 'task',
                    data: task,
                    targetList: 'next-actions',
                    id: task.id
                });
                break;
            case 'projects':
                const { project, nextAction } = await this.createProject(item, decision);
                createdItems.push({
                    type: 'project',
                    data: project,
                    targetList: 'projects',
                    id: project.id
                });
                if (nextAction) {
                    createdItems.push({
                        type: 'task',
                        data: nextAction,
                        targetList: 'next-actions',
                        id: nextAction.id
                    });
                }
                break;
            case 'waiting':
                const waitingItem = await this.createWaitingItem(item, decision);
                createdItems.push({
                    type: 'waiting',
                    data: waitingItem,
                    targetList: 'waiting',
                    id: waitingItem.id
                });
                break;
            case 'calendar':
                const calendarItem = await this.createCalendarItem(item, decision);
                createdItems.push({
                    type: 'calendar',
                    data: calendarItem,
                    targetList: 'calendar',
                    id: calendarItem.id
                });
                break;
            case 'reference':
                const referenceItem = await this.createReferenceItem(item, decision);
                createdItems.push({
                    type: 'reference',
                    data: referenceItem,
                    targetList: 'reference',
                    id: referenceItem.id
                });
                break;
            case 'incubator':
                const incubatorItem = await this.createIncubatorItem(item, decision);
                createdItems.push({
                    type: 'reference', // 暂时使用reference类型
                    data: incubatorItem,
                    targetList: 'incubator',
                    id: incubatorItem.id
                });
                break;
            case 'trash':
                // 垃圾箱不需要创建新条目，只需要标记删除
                this.log('info', '条目移入垃圾箱', { itemId: item.id });
                break;
            default:
                throw new Error(`${constants_1.ERROR_MESSAGES.UNKNOWN_TARGET_LIST}: ${decision.targetList}`);
        }
        return createdItems;
    }
    /**
     * 创建任务
     */
    async createTask(item, decision) {
        const taskData = {
            title: item.description,
            description: item.expectedOutcome,
            category: item.category || 'personal',
            priority: 'medium',
            contexts: this.extractContexts(item.description),
            estimatedMinutes: item.estimatedMinutes,
            dueDate: item.deadline
        };
        // 如果是快速行动，添加特殊标记
        if (decision.quickActionSuggestion) {
            taskData.contexts = [...taskData.contexts, '@快速行动'];
        }
        return await this.taskRepository.create(taskData);
    }
    /**
     * 创建项目（包含下一步行动）
     */
    async createProject(item, decision) {
        // 创建项目
        const projectData = {
            title: item.description,
            description: `基于收集箱条目创建的项目`,
            category: item.category || 'personal',
            expectedOutcome: item.expectedOutcome || '完成项目目标',
            progress: 0
        };
        const project = await this.projectRepository.create(projectData);
        // 创建下一步行动
        const nextActionData = {
            title: `${item.description} - 下一步行动`,
            description: '定义项目的具体下一步行动',
            category: item.category || 'personal',
            priority: 'medium',
            contexts: ['@规划'],
            projectId: project.id,
            estimatedMinutes: 15
        };
        const nextAction = await this.taskRepository.create(nextActionData);
        return { project, nextAction };
    }
    // 其他创建方法的占位符实现
    async createWaitingItem(item, decision) {
        // TODO: 实现等待清单条目创建
        return { id: 'waiting-' + Date.now(), description: item.description };
    }
    async createCalendarItem(item, decision) {
        // TODO: 实现日历条目创建
        return { id: 'calendar-' + Date.now(), title: item.description };
    }
    async createReferenceItem(item, decision) {
        // TODO: 实现参考资料创建
        return { id: 'reference-' + Date.now(), title: item.description };
    }
    async createIncubatorItem(item, decision) {
        // TODO: 实现孵化池条目创建
        return { id: 'incubator-' + Date.now(), description: item.description };
    }
    /**
     * 从描述中提取情境
     */
    extractContexts(description) {
        const contexts = [];
        const text = description.toLowerCase();
        // 简单的情境提取逻辑
        if (text.includes('电脑') || text.includes('网上') || text.includes('在线')) {
            contexts.push('@电脑');
        }
        if (text.includes('电话') || text.includes('打电话')) {
            contexts.push('@电话');
        }
        if (text.includes('外出') || text.includes('去')) {
            contexts.push('@外出');
        }
        return contexts;
    }
    // === 事件系统 ===
    /**
     * 添加事件监听器
     */
    on(event, listener) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(listener);
    }
    /**
     * 触发事件
     */
    emit(event, data) {
        const listeners = this.eventListeners.get(event);
        if (listeners) {
            listeners.forEach(listener => {
                try {
                    listener(data);
                }
                catch (error) {
                    this.log('error', '事件监听器执行错误', { event, error });
                }
            });
        }
    }
    /**
     * 日志记录
     */
    log(level, message, data) {
        if (this.logger) {
            this.logger(level, `[ClarifyService] ${message}`, data);
        }
    }
}
exports.ClarifyService = ClarifyService;
//# sourceMappingURL=clarify-service.js.map