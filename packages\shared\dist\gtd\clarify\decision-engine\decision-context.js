"use strict";
// 决策上下文类 - 管理决策过程中的状态和数据
Object.defineProperty(exports, "__esModule", { value: true });
exports.DecisionContext = void 0;
const constants_1 = require("../constants");
/**
 * 决策上下文实现类
 * 负责管理整个决策过程中的状态、数据和中间结果
 */
class DecisionContext {
    // === 输入数据 ===
    item;
    userPreferences;
    currentWorkload;
    timeContext;
    // === 决策状态 ===
    decision = {};
    reasoning = [];
    appliedRules = [];
    isComplete = false;
    confidence = 0;
    // === 中间计算结果 ===
    estimatedMinutes;
    extractedContexts = [];
    detectedKeywords = [];
    constructor(item, userPreferences, currentWorkload, timeContext) {
        this.item = item;
        this.userPreferences = userPreferences || constants_1.DEFAULT_CONFIG.USER_PREFERENCES;
        this.currentWorkload = currentWorkload;
        this.timeContext = timeContext;
        // 初始化基础决策数据
        this.initializeDecision();
    }
    // === 决策状态管理 ===
    /**
     * 设置可行动性
     */
    setActionable(isActionable, reasoning) {
        this.decision.isActionable = isActionable;
        if (reasoning) {
            this.addReasoning(reasoning);
        }
    }
    /**
     * 设置2分钟规则结果
     */
    setCanDoInTwoMinutes(canDo, reasoning) {
        this.decision.canDoInTwoMinutes = canDo;
        if (canDo) {
            this.decision.quickActionSuggestion = true;
        }
        if (reasoning) {
            this.addReasoning(reasoning);
        }
    }
    /**
     * 设置责任归属
     */
    setMyResponsibility(isMine, reasoning) {
        this.decision.isMyResponsibility = isMine;
        if (reasoning) {
            this.addReasoning(reasoning);
        }
    }
    /**
     * 设置行动复杂度
     */
    setSingleAction(isSingle, reasoning) {
        this.decision.isSingleAction = isSingle;
        if (reasoning) {
            this.addReasoning(reasoning);
        }
    }
    /**
     * 设置时间特定性
     */
    setHasSpecificTime(hasTime, reasoning) {
        this.decision.hasSpecificTime = hasTime;
        if (reasoning) {
            this.addReasoning(reasoning);
        }
    }
    /**
     * 设置目标清单
     */
    setTargetList(targetList, reasoning) {
        this.decision.targetList = targetList;
        if (reasoning) {
            this.addReasoning(reasoning);
        }
    }
    /**
     * 设置快速行动建议
     */
    setQuickActionSuggestion(suggest) {
        this.decision.quickActionSuggestion = suggest;
    }
    // === 推理过程管理 ===
    /**
     * 添加推理信息
     */
    addReasoning(reason) {
        this.reasoning.push(reason);
    }
    /**
     * 记录应用的规则
     */
    addAppliedRule(ruleName) {
        if (!this.appliedRules.includes(ruleName)) {
            this.appliedRules.push(ruleName);
        }
    }
    /**
     * 更新置信度
     */
    updateConfidence(confidence) {
        this.confidence = Math.max(this.confidence, confidence);
    }
    // === 决策完成管理 ===
    /**
     * 标记决策完成
     */
    completeDecision() {
        this.isComplete = true;
    }
    /**
     * 检查决策是否完成
     */
    isDecisionComplete() {
        return this.isComplete || this.hasRequiredDecisions();
    }
    /**
     * 获取最终决策结果
     */
    getDecision() {
        return {
            isActionable: this.decision.isActionable ?? false,
            canDoInTwoMinutes: this.decision.canDoInTwoMinutes,
            isMyResponsibility: this.decision.isMyResponsibility,
            isSingleAction: this.decision.isSingleAction,
            hasSpecificTime: this.decision.hasSpecificTime,
            targetList: this.decision.targetList || this.inferTargetList(),
            quickActionSuggestion: this.decision.quickActionSuggestion,
            reasoning: [...this.reasoning],
            confidence: this.confidence,
            appliedRules: [...this.appliedRules]
        };
    }
    // === 辅助方法 ===
    /**
     * 获取收集箱条目
     */
    getItem() {
        return this.item;
    }
    /**
     * 设置预估时间
     */
    setEstimatedMinutes(minutes) {
        this.estimatedMinutes = minutes;
    }
    /**
     * 获取预估时间
     */
    getEstimatedMinutes() {
        return this.estimatedMinutes || this.item.estimatedMinutes || 0;
    }
    /**
     * 添加检测到的关键词
     */
    addDetectedKeyword(keyword) {
        if (!this.detectedKeywords.includes(keyword)) {
            this.detectedKeywords.push(keyword);
        }
    }
    /**
     * 获取检测到的关键词
     */
    getDetectedKeywords() {
        return [...this.detectedKeywords];
    }
    /**
     * 设置提取的情境
     */
    setExtractedContexts(contexts) {
        this.extractedContexts = contexts;
    }
    /**
     * 获取提取的情境
     */
    getExtractedContexts() {
        return [...this.extractedContexts];
    }
    // === 私有方法 ===
    /**
     * 初始化决策数据
     */
    initializeDecision() {
        // 从收集箱条目中提取初始信息
        if (this.item.estimatedMinutes) {
            this.estimatedMinutes = this.item.estimatedMinutes;
        }
        // 设置初始置信度
        this.confidence = constants_1.CONFIDENCE_THRESHOLDS.LOW;
    }
    /**
     * 检查是否有必需的决策信息
     */
    hasRequiredDecisions() {
        return this.decision.isActionable !== undefined &&
            this.decision.targetList !== undefined;
    }
    /**
     * 推断目标清单
     */
    inferTargetList() {
        // 基于已有决策信息推断目标清单
        if (!this.decision.isActionable) {
            return 'trash'; // 默认非行动项目放入垃圾箱
        }
        if (this.decision.canDoInTwoMinutes) {
            return 'next-actions';
        }
        if (this.decision.isMyResponsibility === false) {
            return 'waiting';
        }
        if (this.decision.isSingleAction === false) {
            return 'projects';
        }
        if (this.decision.hasSpecificTime) {
            return 'calendar';
        }
        return 'next-actions'; // 默认放入执行清单
    }
}
exports.DecisionContext = DecisionContext;
//# sourceMappingURL=decision-context.js.map