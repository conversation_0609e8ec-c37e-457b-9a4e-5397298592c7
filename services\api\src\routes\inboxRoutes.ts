import { Express, Request, Response } from 'express'
import { DatabaseManager } from '@focusguard/shared'

/**
 * 设置收集箱相关路由
 */
export function setupInboxRoutes(app: Express, prefix: string, databaseManager: DatabaseManager) {
  const inboxRepository = databaseManager.getInboxRepository()

  /**
   * GET /api/inbox/count - 获取收集箱统计
   */
  app.get(`${prefix}/inbox/count`, async (req: Request, res: Response) => {
    try {
      const totalCount = await inboxRepository.count()
      const capturedCount = await inboxRepository.count({ status: 'captured' })
      const processingCount = await inboxRepository.count({ status: 'processing' })
      const clarifiedCount = await inboxRepository.count({ status: 'clarified' })

      res.json({
        success: true,
        data: {
          total: totalCount,
          captured: capturedCount,
          processing: processingCount,
          clarified: clarifiedCount
        }
      })
    } catch (error) {
      console.error('获取收集箱统计错误:', error)
      res.status(500).json({
        success: false,
        error: '服务器内部错误'
      })
    }
  })

  /**
   * GET /api/inbox - 获取收集箱列表
   */
  app.get(`${prefix}/inbox`, async (req: Request, res: Response) => {
    try {
      const { status, limit = 50, offset = 0 } = req.query

      let items
      if (status && typeof status === 'string') {
        items = await inboxRepository.findByStatus(status as any, {
          limit: Number(limit),
          offset: Number(offset),
          orderBy: 'created_at',
          orderDirection: 'DESC'
        })
      } else {
        items = await inboxRepository.findAll({
          limit: Number(limit),
          offset: Number(offset),
          orderBy: 'created_at',
          orderDirection: 'DESC'
        })
      }

      // 转换日期格式为字符串
      const formattedItems = items.map((item: any) => ({
        ...item,
        createdAt: item.createdAt.toISOString(),
        updatedAt: item.updatedAt.toISOString(),
        deadline: item.deadline?.toISOString()
      }))

      res.json({
        success: true,
        data: formattedItems
      })
    } catch (error) {
      console.error('获取收集箱列表错误:', error)
      res.status(500).json({
        success: false,
        error: '服务器内部错误'
      })
    }
  })

  /**
   * GET /api/inbox/:id - 获取单个收集箱条目
   */
  app.get(`${prefix}/inbox/:id`, async (req: Request, res: Response) => {
    try {
      const { id } = req.params

      const item = await inboxRepository.findById(id)
      
      if (!item) {
        return res.status(404).json({
          success: false,
          error: '收集箱条目不存在'
        })
      }

      // 转换日期格式
      const formattedItem = {
        ...item,
        createdAt: item.createdAt.toISOString(),
        updatedAt: item.updatedAt.toISOString(),
        deadline: item.deadline?.toISOString()
      }

      res.json({
        success: true,
        data: formattedItem
      })
    } catch (error) {
      console.error('获取收集箱条目错误:', error)
      res.status(500).json({
        success: false,
        error: '服务器内部错误'
      })
    }
  })

  /**
   * PUT /api/inbox/:id - 更新收集箱条目
   */
  app.put(`${prefix}/inbox/:id`, async (req: Request, res: Response) => {
    try {
      const { id } = req.params
      const updates = req.body

      // 检查条目是否存在
      const existingItem = await inboxRepository.findById(id)
      if (!existingItem) {
        return res.status(404).json({
          success: false,
          error: '收集箱条目不存在'
        })
      }

      // 处理日期字段
      if (updates.deadline) {
        try {
          updates.deadline = new Date(updates.deadline)
          if (isNaN(updates.deadline.getTime())) {
            return res.status(400).json({
              success: false,
              error: '截止日期格式无效'
            })
          }
        } catch (error) {
          return res.status(400).json({
            success: false,
            error: '截止日期格式无效'
          })
        }
      }

      // 验证分类
      if (updates.category) {
        const validCategories = ['work', 'personal', 'health', 'learning', 'leisure']
        if (!validCategories.includes(updates.category)) {
          return res.status(400).json({
            success: false,
            error: '无效的分类'
          })
        }
      }

      // 验证状态
      if (updates.status) {
        const validStatuses = ['captured', 'processing', 'clarified', 'archived']
        if (!validStatuses.includes(updates.status)) {
          return res.status(400).json({
            success: false,
            error: '无效的状态'
          })
        }
      }

      await inboxRepository.update(id, updates)

      res.json({
        success: true,
        message: '更新成功'
      })
    } catch (error) {
      console.error('更新收集箱条目错误:', error)
      res.status(500).json({
        success: false,
        error: '服务器内部错误'
      })
    }
  })

  /**
   * DELETE /api/inbox/:id - 删除收集箱条目
   */
  app.delete(`${prefix}/inbox/:id`, async (req: Request, res: Response) => {
    try {
      const { id } = req.params

      // 检查条目是否存在
      const existingItem = await inboxRepository.findById(id)
      if (!existingItem) {
        return res.status(404).json({
          success: false,
          error: '收集箱条目不存在'
        })
      }

      await inboxRepository.delete(id)

      res.json({
        success: true,
        message: '删除成功'
      })
    } catch (error) {
      console.error('删除收集箱条目错误:', error)
      res.status(500).json({
        success: false,
        error: '服务器内部错误'
      })
    }
  })


}
