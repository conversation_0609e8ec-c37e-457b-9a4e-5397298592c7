# GTD 明晰模块

本模块提供了GTD（Getting Things Done）明晰流程的两个版本实现：

## 版本对比

### MVP版本 - 用户主导决策 🎯
**适用场景**: 初期产品、用户学习GTD、需要用户参与决策

**特点**:
- ✅ 向导式界面，逐步引导用户
- ✅ 每一步都由用户自己判断和选择
- ✅ 教育性强，帮助用户理解GTD流程
- ✅ 实现简单，易于维护
- ✅ 用户有完全控制权

**工作流程**:
```
收集箱条目 → 向导问题1 → 用户选择 → 向导问题2 → 用户选择 → ... → 最终结果
```

### Pro版本 - AI辅助决策 🤖
**适用场景**: 高级用户、批量处理、AI集成

**特点**:
- ✅ 智能分析文本内容
- ✅ 自动推理和决策
- ✅ 支持批量处理
- ✅ 可配置的决策规则
- ✅ 高效处理大量条目

**工作流程**:
```
收集箱条目 → AI分析 → 自动决策 → 建议结果 → 用户确认 → 最终结果
```

## 使用示例

### MVP版本使用

```typescript
import { ClarifyModuleFactory } from '@/gtd/clarify';

// 创建MVP服务
const clarifyService = ClarifyModuleFactory.createMVPService(
  inboxRepository,
  taskRepository, 
  projectRepository
);

// 开始明晰流程
const session = await clarifyService.startClarification('item-123');

// 获取当前问题
const question = clarifyService.getCurrentQuestion(session.id);
console.log(question.title); // "这是一个可以行动的事项吗？"

// 用户提交答案
await clarifyService.submitAnswer(session.id, true);

// 继续下一步...
const nextQuestion = clarifyService.getCurrentQuestion(session.id);

// 完成明晰
const result = await clarifyService.completeClarification(session.id);
```

### Pro版本使用

```typescript
import { ClarifyModuleFactory } from '@/gtd/clarify';

// 创建Pro服务
const clarifyService = ClarifyModuleFactory.createProService(
  inboxRepository,
  taskRepository,
  projectRepository
);

// 直接明晰单个条目
const result = await clarifyService.clarifyItem('item-123');

// 批量明晰
const results = await clarifyService.batchClarify(['item-1', 'item-2', 'item-3']);
```

## 决策流程

两个版本都遵循相同的GTD决策流程：

```
1. 可行动吗？
   ├─ 否 → 垃圾箱/知识库/孵化池
   └─ 是 ↓

2. 2分钟内能完成吗？
   ├─ 是 → 立即执行
   └─ 否 ↓

3. 该我做吗？
   ├─ 否 → 等待清单（委派）
   └─ 是 ↓

4. 是单一行动吗？
   ├─ 否 → 项目清单
   └─ 是 ↓

5. 有特定时间吗？
   ├─ 是 → 日历
   └─ 否 → 执行清单
```

## 目标清单

明晰后的条目会被分配到以下清单之一：

- **立即执行**: 2分钟内可完成的快速行动
- **执行清单**: 需要安排时间完成的单一行动
- **项目清单**: 需要多个步骤的复杂项目
- **等待清单**: 委派给他人的任务
- **日历**: 有特定时间要求的事项
- **知识库**: 参考资料和信息
- **孵化池**: 将来可能要做的事项

## 推荐使用策略

### 产品初期
```typescript
// 推荐使用MVP版本，帮助用户学习GTD
const service = ClarifyModuleFactory.createMVPService(...);
```

### 用户熟练后
```typescript
// 可以升级到Pro版本，提高效率
const service = ClarifyModuleFactory.createProService(...);
```

### 根据用户经验自动选择
```typescript
const service = ClarifyModuleFactory.getRecommendedService(
  userExperience, // 'beginner' | 'intermediate' | 'advanced'
  inboxRepository,
  taskRepository,
  projectRepository
);
```

## 扩展性

### 自定义决策规则（Pro版本）
```typescript
import { CustomRule } from './my-custom-rule';

const customEngine = DecisionEngineFactory.createCustomEngine([
  ActionabilityRule,
  TimeRule,
  CustomRule // 你的自定义规则
]);

const service = ClarifyModuleFactory.createCustomProService(
  inboxRepository,
  taskRepository,
  projectRepository,
  customEngine
);
```

### 自定义向导步骤（MVP版本）
MVP版本的向导步骤可以通过修改 `ClarifyWizard` 类来自定义。

## 性能对比

| 特性 | MVP版本 | Pro版本 |
|------|---------|---------|
| 处理速度 | 慢（需要用户交互） | 快（自动处理） |
| 批量处理 | 不支持 | 支持 |
| 学习成本 | 低（引导式） | 高（需要理解规则） |
| 准确性 | 高（用户决策） | 中（依赖AI质量） |
| 用户参与度 | 高 | 低 |

## 最佳实践

1. **新用户**: 从MVP版本开始，让用户熟悉GTD流程
2. **批量处理**: 使用Pro版本处理大量历史数据
3. **混合使用**: 重要事项用MVP版本仔细考虑，日常事项用Pro版本快速处理
4. **渐进升级**: 随着用户熟练度提升，逐步引入Pro版本功能

## 技术架构

```
明晰模块
├── MVP版本
│   ├── ClarifyServiceMVP (主服务)
│   ├── ClarifyWizard (向导引擎)
│   └── 向导步骤定义
└── Pro版本
    ├── ClarifyService (主服务)
    ├── DecisionTree (决策树)
    ├── DecisionEngine (决策引擎)
    └── 决策规则集合
```

这种双版本设计既满足了MVP的简单需求，又为未来的AI增强功能预留了空间。
