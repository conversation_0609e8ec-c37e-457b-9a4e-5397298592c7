"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setupCaptureRoutes = setupCaptureRoutes;
const shared_1 = require("@focusguard/shared");
/**
 * 设置捕获相关路由
 */
function setupCaptureRoutes(app, prefix, databaseManager) {
    // 创建捕获服务实例
    const inboxRepository = databaseManager.getInboxRepository();
    const captureService = shared_1.CaptureModuleFactory.createCaptureService(inboxRepository);
    /**
     * POST /api/capture - 捕获任务
     */
    app.post(`${prefix}/capture`, async (req, res) => {
        try {
            const { description, expectedOutcome, deadline, estimatedMinutes, category } = req.body;
            // 验证必填字段
            if (!description || typeof description !== 'string' || !description.trim()) {
                return res.status(400).json({
                    success: false,
                    error: '任务描述不能为空'
                });
            }
            // 构建完整的任务描述（包含所有信息）
            let fullDescription = description.trim();
            // 如果有预期结果，添加到描述中
            if (expectedOutcome && expectedOutcome.trim()) {
                fullDescription += ` [预期结果: ${expectedOutcome.trim()}]`;
            }
            // 如果有截止日期，添加到描述中
            if (deadline) {
                try {
                    const deadlineDate = new Date(deadline);
                    if (isNaN(deadlineDate.getTime())) {
                        return res.status(400).json({
                            success: false,
                            error: '截止日期格式无效'
                        });
                    }
                    fullDescription += ` [截止: ${deadlineDate.toLocaleDateString()}]`;
                }
                catch (error) {
                    return res.status(400).json({
                        success: false,
                        error: '截止日期格式无效'
                    });
                }
            }
            // 如果有预估时间，添加到描述中
            if (estimatedMinutes && typeof estimatedMinutes === 'number' && estimatedMinutes > 0) {
                fullDescription += ` [预估: ${estimatedMinutes}分钟]`;
            }
            // 如果有分类，添加到描述中
            if (category && typeof category === 'string') {
                const validCategories = ['work', 'personal', 'health', 'learning', 'leisure'];
                if (validCategories.includes(category)) {
                    fullDescription += ` [分类: ${category}]`;
                }
            }
            // 执行捕获 - 传递字符串作为原始输入
            const result = await captureService.capture(fullDescription, 'manual');
            if (result.success) {
                res.json({
                    success: true,
                    data: result
                });
            }
            else {
                res.status(400).json({
                    success: false,
                    error: result.error || '捕获失败'
                });
            }
        }
        catch (error) {
            console.error('捕获任务错误:', error);
            res.status(500).json({
                success: false,
                error: '服务器内部错误'
            });
        }
    });
    /**
     * POST /api/capture/batch - 批量捕获任务
     */
    app.post(`${prefix}/capture/batch`, async (req, res) => {
        try {
            const { items } = req.body;
            if (!Array.isArray(items) || items.length === 0) {
                return res.status(400).json({
                    success: false,
                    error: '批量捕获需要提供任务列表'
                });
            }
            // 验证每个任务并构建输入
            const captureInputs = [];
            for (let i = 0; i < items.length; i++) {
                const item = items[i];
                if (!item.description || typeof item.description !== 'string' || !item.description.trim()) {
                    return res.status(400).json({
                        success: false,
                        error: `第${i + 1}个任务描述不能为空`
                    });
                }
                // 构建完整描述
                let fullDescription = item.description.trim();
                if (item.category) {
                    fullDescription += ` [分类: ${item.category}]`;
                }
                if (item.estimatedMinutes) {
                    fullDescription += ` [预估: ${item.estimatedMinutes}分钟]`;
                }
                captureInputs.push({
                    rawInput: fullDescription,
                    source: 'manual'
                });
            }
            // 执行批量捕获
            const result = await captureService.batchCapture(captureInputs);
            res.json({
                success: true,
                data: result
            });
        }
        catch (error) {
            console.error('批量捕获任务错误:', error);
            res.status(500).json({
                success: false,
                error: '服务器内部错误'
            });
        }
    });
    /**
     * GET /api/capture/stats - 获取捕获统计
     */
    app.get(`${prefix}/capture/stats`, async (req, res) => {
        try {
            const stats = captureService.getStats();
            res.json({
                success: true,
                data: stats
            });
        }
        catch (error) {
            console.error('获取捕获统计错误:', error);
            res.status(500).json({
                success: false,
                error: '服务器内部错误'
            });
        }
    });
    /**
     * GET /api/capture/sources - 获取支持的输入源
     */
    app.get(`${prefix}/capture/sources`, async (req, res) => {
        try {
            const sources = captureService.getSupportedSources();
            res.json({
                success: true,
                data: sources
            });
        }
        catch (error) {
            console.error('获取输入源错误:', error);
            res.status(500).json({
                success: false,
                error: '服务器内部错误'
            });
        }
    });
}
//# sourceMappingURL=captureRoutes.js.map