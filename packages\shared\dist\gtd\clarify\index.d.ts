export { ClarifyServiceMVP } from './clarify-service-mvp';
export { ClarifyWizard } from './wizard/clarify-wizard';
export type { WizardSession, WizardAnswers, StepQuestion, StepOption, ClarifyStep, ClarificationResult } from './wizard/clarify-wizard';
export { ClarifyService } from './clarify-service';
export { DecisionEngineFactory } from './decision-engine';
export type { ClarifyDecision, TargetList, UserPreferences, WorkloadInfo, TimeContext, DecisionRule, RuleResult, BatchClarifyOptions, BatchClarifyResult } from './types';
export { RULE_PRIORITIES, CONFIDENCE_THRESHOLDS, TIME_CONSTANTS, DEFAULT_CONFIG } from './constants';
import { ClarifyServiceMVP } from './clarify-service-mvp';
import { ClarifyService } from './clarify-service';
import { InboxRepository, TaskRepository, ProjectRepository } from '../../types/database';
/**
 * 明晰模块工厂类
 * 提供便捷的服务创建方法，支持MVP和Pro两个版本
 */
export declare class ClarifyModuleFactory {
    /**
     * 创建MVP版本明晰服务（用户主导决策）
     * 推荐用于初期产品和用户学习GTD流程
     */
    static createMVPService(inboxRepository: InboxRepository, taskRepository: TaskRepository, projectRepository: ProjectRepository, logger?: (level: string, message: string, data?: any) => void): ClarifyServiceMVP;
    /**
     * 创建Pro版本明晰服务（AI辅助决策）
     * 包含完整的GTD决策引擎，适合高级用户
     */
    static createProService(inboxRepository: InboxRepository, taskRepository: TaskRepository, projectRepository: ProjectRepository, logger?: (level: string, message: string, data?: any) => void): ClarifyService;
    /**
     * 创建标准明晰服务（向后兼容）
     * 默认创建Pro版本
     */
    static createClarifyService(inboxRepository: InboxRepository, taskRepository: TaskRepository, projectRepository: ProjectRepository, logger?: (level: string, message: string, data?: any) => void): ClarifyService;
    /**
     * 创建自定义Pro版本明晰服务
     * 允许指定自定义的决策引擎
     */
    static createCustomProService(inboxRepository: InboxRepository, taskRepository: TaskRepository, projectRepository: ProjectRepository, decisionEngine: any, logger?: (level: string, message: string, data?: any) => void): ClarifyService;
    /**
     * 获取推荐的服务版本
     * 根据用户经验水平推荐合适的版本
     */
    static getRecommendedService(userExperience: 'beginner' | 'intermediate' | 'advanced', inboxRepository: InboxRepository, taskRepository: TaskRepository, projectRepository: ProjectRepository, logger?: (level: string, message: string, data?: any) => void): ClarifyServiceMVP | ClarifyService;
    /**
     * 创建最小化Pro版本明晰服务
     * 只包含基本的决策规则
     */
    static createMinimalProService(inboxRepository: InboxRepository, taskRepository: TaskRepository, projectRepository: ProjectRepository, logger?: (level: string, message: string, data?: any) => void): ClarifyService;
    /**
     * 获取支持的目标清单类型
     */
    static getSupportedTargetLists(): string[];
    /**
     * 获取决策规则信息
     */
    static getDecisionRulesInfo(): {
        name: string;
        priority: number;
        description: string;
    }[];
    /**
     * 验证明晰服务配置
     */
    static validateConfiguration(config: {
        inboxRepository: any;
        taskRepository: any;
        projectRepository: any;
    }): boolean;
}
/**
 * 明晰服务健康检查
 */
export declare class ClarifyHealthChecker {
    private clarifyService;
    constructor(clarifyService: ClarifyService);
    /**
     * 执行健康检查
     */
    healthCheck(): Promise<{
        status: 'healthy' | 'unhealthy';
        checks: Array<{
            name: string;
            status: 'pass' | 'fail';
            message?: string;
        }>;
    }>;
}
/**
 * 明晰统计信息收集器
 */
export declare class ClarifyStatsCollector {
    private stats;
    /**
     * 记录明晰结果
     */
    recordClarification(result: any): void;
    /**
     * 记录错误
     */
    recordError(): void;
    /**
     * 获取统计信息
     */
    getStats(): {
        byTargetList: {
            [k: string]: number;
        };
        successRate: number;
        totalClarified: number;
        averageProcessingTime: number;
        totalProcessingTime: number;
        errorCount: number;
    };
    /**
     * 重置统计信息
     */
    reset(): void;
}
//# sourceMappingURL=index.d.ts.map