// 时间规则 - 2分钟规则的实现

import { DecisionRule, RuleResult, IDecisionContext } from '../../types';
import { DecisionContext } from '../decision-context';
import { 
  RULE_PRIORITIES, 
  TIME_CONSTANTS,
  CONFIDENCE_THRESHOLDS 
} from '../../constants';

/**
 * 时间规则 - 实现GTD的2分钟规则
 * 如果一个任务能在2分钟内完成，建议立即执行
 */
export class TimeRule implements DecisionRule {
  public readonly name = 'TimeRule';
  public readonly priority = RULE_PRIORITIES.TIME;

  async evaluate(context: DecisionContext): Promise<RuleResult> {
    const item = context.getItem();
    
    // 只对可行动事项应用时间规则
    const decision = context.getDecision();
    if (decision.isActionable === false) {
      return {
        ruleName: this.name,
        decision: {},
        confidence: CONFIDENCE_THRESHOLDS.HIGH,
        reasoning: ['跳过时间规则：非行动事项'],
        shouldContinue: true
      };
    }

    // 获取或估算时间
    const estimatedMinutes = this.getEstimatedTime(item, context);
    
    // 应用2分钟规则
    const canDoInTwoMinutes = estimatedMinutes <= (context.userPreferences?.twoMinuteThreshold || 2);
    
    // 构建推理过程
    const reasoning = this.buildReasoning(estimatedMinutes, canDoInTwoMinutes, context.userPreferences?.twoMinuteThreshold || 2);
    
    // 计算置信度
    const confidence = this.calculateConfidence(estimatedMinutes, item);

    return {
      ruleName: this.name,
      decision: {
        canDoInTwoMinutes,
        quickActionSuggestion: canDoInTwoMinutes
      },
      confidence,
      reasoning,
      shouldContinue: true // 继续执行后续规则
    };
  }

  /**
   * 获取或估算任务时间
   */
  private getEstimatedTime(item: any, context: DecisionContext): number {
    // 1. 使用已有的预估时间
    if (item.estimatedMinutes && item.estimatedMinutes > 0) {
      context.setEstimatedMinutes(item.estimatedMinutes);
      return item.estimatedMinutes;
    }

    // 2. 基于描述长度和复杂度估算
    const estimated = this.estimateTimeFromDescription(item.description);
    context.setEstimatedMinutes(estimated);
    return estimated;
  }

  /**
   * 基于描述估算时间
   */
  private estimateTimeFromDescription(description: string): number {
    const text = description.toLowerCase();

    // 快速任务指示词
    const quickTaskIndicators = [
      '发送', '回复', '转发', '下载', '保存', '删除', '检查',
      '确认', '查看', '打开', '关闭', '点击', '复制', '粘贴'
    ];

    // 中等任务指示词
    const mediumTaskIndicators = [
      '写', '编辑', '修改', '整理', '分析', '计算', '设计',
      '准备', '制作', '创建', '更新', '安装', '配置'
    ];

    // 复杂任务指示词
    const complexTaskIndicators = [
      '开发', '实施', '建立', '构建', '研究', '学习', '培训',
      '规划', '策划', '组织', '协调', '管理', '优化'
    ];

    // 检查任务复杂度
    let estimatedMinutes: number = TIME_CONSTANTS.DEFAULT_TASK_ESTIMATE;

    if (quickTaskIndicators.some(indicator => text.includes(indicator))) {
      estimatedMinutes = 5; // 5分钟
    } else if (complexTaskIndicators.some(indicator => text.includes(indicator))) {
      estimatedMinutes = 60; // 1小时
    } else if (mediumTaskIndicators.some(indicator => text.includes(indicator))) {
      estimatedMinutes = 30; // 30分钟
    }

    // 基于描述长度调整
    if (description.length < 20) {
      estimatedMinutes = Math.max(1, estimatedMinutes * 0.5);
    } else if (description.length > 100) {
      estimatedMinutes = estimatedMinutes * 1.5;
    }

    // 检查是否有时间相关的明确指示
    estimatedMinutes = this.adjustForTimeIndicators(text, estimatedMinutes);

    return Math.round(estimatedMinutes);
  }

  /**
   * 根据时间指示词调整估算
   */
  private adjustForTimeIndicators(text: string, baseEstimate: number): number {
    // 快速指示词
    const quickIndicators = [
      '快速', '立即', '马上', '赶紧', '简单', '容易', '一下'
    ];

    // 耗时指示词
    const timeConsumingIndicators = [
      '详细', '仔细', '深入', '全面', '完整', '彻底', '复杂'
    ];

    if (quickIndicators.some(indicator => text.includes(indicator))) {
      return Math.max(1, baseEstimate * 0.3);
    }

    if (timeConsumingIndicators.some(indicator => text.includes(indicator))) {
      return baseEstimate * 2;
    }

    // 检查具体时间表达
    const timePatterns = [
      { pattern: /(\d+)分钟/, multiplier: 1 },
      { pattern: /(\d+)小时/, multiplier: 60 },
      { pattern: /半小时|30分/, multiplier: 30 },
      { pattern: /一小时/, multiplier: 60 }
    ];

    for (const { pattern, multiplier } of timePatterns) {
      const match = text.match(pattern);
      if (match) {
        if (pattern.source.includes('半小时|30分')) {
          return 30;
        } else if (pattern.source.includes('一小时')) {
          return 60;
        } else {
          const number = parseInt(match[1]);
          return number * multiplier;
        }
      }
    }

    return baseEstimate;
  }

  /**
   * 构建推理过程
   */
  private buildReasoning(
    estimatedMinutes: number, 
    canDoInTwoMinutes: boolean,
    threshold: number
  ): string[] {
    const reasoning: string[] = [];

    reasoning.push(`预估时间: ${estimatedMinutes}分钟`);
    reasoning.push(`2分钟阈值: ${threshold}分钟`);

    if (canDoInTwoMinutes) {
      reasoning.push('✅ 符合2分钟规则，建议立即执行');
      reasoning.push('立即执行比管理这个任务更高效');
    } else {
      reasoning.push('❌ 超过2分钟阈值，需要进一步处理');
      reasoning.push('任务需要放入适当的清单进行管理');
    }

    // 添加时间估算的依据
    if (estimatedMinutes <= 1) {
      reasoning.push('极简任务，几乎不需要时间');
    } else if (estimatedMinutes <= 5) {
      reasoning.push('快速任务，可以快速完成');
    } else if (estimatedMinutes <= 15) {
      reasoning.push('短期任务，需要专注时间');
    } else if (estimatedMinutes <= 60) {
      reasoning.push('中等任务，需要安排时间块');
    } else {
      reasoning.push('长期任务，可能需要分解');
    }

    return reasoning;
  }

  /**
   * 计算置信度
   */
  private calculateConfidence(estimatedMinutes: number, item: any): number {
    let confidence = CONFIDENCE_THRESHOLDS.MEDIUM;

    // 如果有明确的时间估算，提高置信度
    if (item.estimatedMinutes && item.estimatedMinutes > 0) {
      confidence += 0.3;
    }

    // 极端值降低置信度
    if (estimatedMinutes < 0.5 || estimatedMinutes > 240) {
      confidence -= 0.2;
    }

    // 在2分钟阈值附近的任务，降低置信度（边界情况）
    const threshold = TIME_CONSTANTS.TWO_MINUTE_THRESHOLD;
    if (Math.abs(estimatedMinutes - threshold) < 0.5) {
      confidence -= 0.1;
    }

    return Math.min(1.0, Math.max(CONFIDENCE_THRESHOLDS.LOW, confidence));
  }
}
