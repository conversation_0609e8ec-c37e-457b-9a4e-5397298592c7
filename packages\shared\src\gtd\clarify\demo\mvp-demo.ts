// GTD 明晰 MVP版本演示脚本

import { ClarifyServiceMVP } from '../clarify-service-mvp';
import { InboxItem } from '../../../types';

// 模拟仓储实现
class MockInboxRepository {
  private items = new Map<string, InboxItem>();

  async findById(id: string): Promise<InboxItem | null> {
    return this.items.get(id) || null;
  }

  async update(id: string, updates: Partial<InboxItem>): Promise<InboxItem> {
    const item = this.items.get(id);
    if (!item) throw new Error(`条目不存在: ${id}`);
    
    const updated = { ...item, ...updates, updatedAt: new Date() };
    this.items.set(id, updated);
    return updated;
  }

  async create(item: Partial<InboxItem>): Promise<InboxItem> {
    const newItem: InboxItem = {
      id: item.id || `item-${Date.now()}`,
      description: item.description || '',
      source: item.source || 'manual',
      status: item.status || 'captured',
      createdAt: new Date(),
      updatedAt: new Date(),
      version: 1,
      ...item
    };
    this.items.set(newItem.id, newItem);
    return newItem;
  }

  // 添加条目用于演示
  addItem(item: InboxItem) {
    this.items.set(item.id, item);
  }
}

class MockTaskRepository {
  private tasks = new Map();

  async create(taskData: any) {
    const task = {
      id: `task-${Date.now()}`,
      ...taskData,
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date(),
      version: 1
    };
    this.tasks.set(task.id, task);
    console.log(`✅ 创建任务: ${task.title}`);
    return task;
  }

  async findById(id: string) { return this.tasks.get(id); }
  async update() { return {}; }
  async delete() {}
  async findAll() { return []; }
}

class MockProjectRepository {
  private projects = new Map();

  async create(projectData: any) {
    const project = {
      id: `project-${Date.now()}`,
      ...projectData,
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date(),
      version: 1
    };
    this.projects.set(project.id, project);
    console.log(`✅ 创建项目: ${project.title}`);
    return project;
  }

  async findById(id: string) { return this.projects.get(id); }
  async update() { return {}; }
  async delete() {}
  async findAll() { return []; }
}

/**
 * MVP版本演示
 */
export class MVPDemo {
  private clarifyService: ClarifyServiceMVP;
  private inboxRepo: MockInboxRepository;

  constructor() {
    this.inboxRepo = new MockInboxRepository();
    const taskRepo = new MockTaskRepository();
    const projectRepo = new MockProjectRepository();

    this.clarifyService = new ClarifyServiceMVP(
      this.inboxRepo as any,
      taskRepo as any,
      projectRepo as any,
      (level, message, data) => {
        console.log(`[${level.toUpperCase()}] ${message}`, data ? JSON.stringify(data, null, 2) : '');
      }
    );
  }

  /**
   * 演示完整的明晰流程
   */
  async demonstrateFullFlow() {
    console.log('🚀 GTD 明晰 MVP版本演示\n');

    // 1. 准备测试数据
    const testItem: InboxItem = {
      id: 'demo-item-1',
      description: '让小王帮忙整理下周会议的资料',
      source: 'manual',
      status: 'captured',
      createdAt: new Date(),
      updatedAt: new Date(),
      version: 1
    };

    this.inboxRepo.addItem(testItem);
    console.log('📝 收集箱条目:', testItem.description);
    console.log('');

    try {
      // 2. 开始明晰流程
      console.log('🎯 开始明晰流程...');
      const session = await this.clarifyService.startClarification(testItem.id);
      console.log(`✅ 创建会话: ${session.id}`);
      console.log('');

      // 3. 模拟用户交互流程
      await this.simulateUserInteraction(session.id);

    } catch (error) {
      console.error('❌ 演示失败:', error);
    }
  }

  /**
   * 模拟用户交互
   */
  private async simulateUserInteraction(sessionId: string) {
    let stepNumber = 1;

    while (true) {
      // 获取当前问题
      const question = this.clarifyService.getCurrentQuestion(sessionId);
      if (!question) {
        console.log('🎉 所有问题已完成！');
        break;
      }

      // 显示问题
      console.log(`📋 第${stepNumber}步: ${question.title}`);
      console.log(`💭 ${question.description}`);
      
      if (question.helpText) {
        console.log(`💡 提示: ${question.helpText}`);
      }

      console.log('\n🔘 选项:');
      question.options.forEach((option, index) => {
        console.log(`   ${index + 1}. ${option.label}`);
        console.log(`      ${option.description}`);
      });
      console.log('');

      // 模拟用户选择（预设的选择路径）
      const { choice, additionalData } = this.getPresetChoice(stepNumber, question);
      
      console.log(`👤 用户选择: ${choice + 1}. ${question.options[choice].label}`);
      if (additionalData && Object.keys(additionalData).length > 0) {
        console.log(`📝 额外信息:`, additionalData);
      }

      // 提交答案
      await this.clarifyService.submitAnswer(
        sessionId, 
        question.options[choice].value, 
        additionalData
      );

      console.log('✅ 答案已保存\n');
      console.log('─'.repeat(50));
      console.log('');

      stepNumber++;
    }

    // 完成明晰
    console.log('🏁 完成明晰流程...');
    const result = await this.clarifyService.completeClarification(sessionId);
    
    console.log('🎊 明晰完成！');
    console.log('');
    console.log('📊 最终结果:');
    console.log(`   目标清单: ${result.targetList}`);
    console.log(`   决策摘要: ${result.summary}`);
    console.log(`   创建条目: ${result.createdItems.length}个`);
    console.log(`   收集箱状态: ${result.updatedInboxItem.status}`);
    console.log('');

    if (result.createdItems.length > 0) {
      console.log('📋 创建的条目:');
      result.createdItems.forEach((item, index) => {
        console.log(`   ${index + 1}. ${item.type}: ${item.data.title || item.data.description}`);
      });
    }
  }

  /**
   * 预设的用户选择路径
   */
  private getPresetChoice(stepNumber: number, question: any): { choice: number; additionalData?: any } {
    switch (stepNumber) {
      case 1: // 可行动性
        return { choice: 0 }; // 是，需要行动
      
      case 2: // 时间
        return { choice: 1 }; // 不是，需要更多时间
      
      case 3: // 责任归属
        return { 
          choice: 1, // 不是，需要委派
          additionalData: { delegatedTo: '小王' }
        };
      
      default:
        return { choice: 0 }; // 默认选择第一个选项
    }
  }

  /**
   * 演示多个不同场景
   */
  async demonstrateMultipleScenarios() {
    console.log('🎭 多场景演示\n');

    const scenarios = [
      {
        description: '发送邮件给客户',
        estimatedMinutes: 1,
        path: [0, 0] // 可行动 → 2分钟内完成
      },
      {
        description: '写下周的工作报告',
        estimatedMinutes: 60,
        path: [0, 1, 0, 0, 1] // 可行动 → 需要更多时间 → 我来做 → 单一行动 → 时间灵活
      },
      {
        description: '组织公司年会',
        path: [0, 1, 0, 1] // 可行动 → 需要更多时间 → 我来做 → 多步骤项目
      }
    ];

    for (let i = 0; i < scenarios.length; i++) {
      const scenario = scenarios[i];
      console.log(`\n=== 场景 ${i + 1}: ${scenario.description} ===`);
      
      const testItem: InboxItem = {
        id: `demo-item-${i + 2}`,
        description: scenario.description,
        estimatedMinutes: scenario.estimatedMinutes,
        source: 'manual',
        status: 'captured',
        createdAt: new Date(),
        updatedAt: new Date(),
        version: 1
      };

      this.inboxRepo.addItem(testItem);
      
      try {
        const session = await this.clarifyService.startClarification(testItem.id);
        await this.simulateScenarioPath(session.id, scenario.path);
      } catch (error) {
        console.error(`❌ 场景 ${i + 1} 失败:`, error);
      }
    }
  }

  /**
   * 模拟特定场景路径
   */
  private async simulateScenarioPath(sessionId: string, path: number[]) {
    let stepIndex = 0;

    while (stepIndex < path.length) {
      const question = this.clarifyService.getCurrentQuestion(sessionId);
      if (!question) break;

      const choiceIndex = path[stepIndex];
      const choice = question.options[choiceIndex];
      
      console.log(`👤 选择: ${choice.label}`);
      
      let additionalData: any = {};
      if (choice.requiresInput) {
        switch (choice.inputType) {
          case 'person':
            additionalData.delegatedTo = '同事';
            break;
          case 'text':
            additionalData.projectOutcome = '成功完成项目目标';
            break;
          case 'date':
            additionalData.calendarTime = new Date();
            break;
        }
      }

      await this.clarifyService.submitAnswer(sessionId, choice.value, additionalData);
      stepIndex++;
    }

    const result = await this.clarifyService.completeClarification(sessionId);
    console.log(`🎯 结果: ${result.targetList} - ${result.summary}`);
  }
}

// 运行演示
async function runDemo() {
  const demo = new MVPDemo();
  
  try {
    await demo.demonstrateFullFlow();
    console.log('\n' + '='.repeat(60) + '\n');
    await demo.demonstrateMultipleScenarios();
  } catch (error) {
    console.error('演示运行失败:', error);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  runDemo();
}

export { runDemo };
