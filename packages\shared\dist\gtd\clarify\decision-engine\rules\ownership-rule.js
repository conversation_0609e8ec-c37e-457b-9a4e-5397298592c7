"use strict";
// 责任归属规则 - 判断任务是否需要委派
Object.defineProperty(exports, "__esModule", { value: true });
exports.OwnershipRule = void 0;
const constants_1 = require("../../constants");
/**
 * 责任归属规则
 * 判断任务是否该由自己执行，还是需要委派给他人
 */
class OwnershipRule {
    name = 'OwnershipRule';
    priority = constants_1.RULE_PRIORITIES.OWNERSHIP;
    async evaluate(context) {
        const item = context.getItem();
        // 只对可行动且非快速行动的事项应用责任归属规则
        const decision = context.getDecision();
        if (decision.isActionable === false) {
            return {
                ruleName: this.name,
                decision: {},
                confidence: constants_1.CONFIDENCE_THRESHOLDS.HIGH,
                reasoning: ['跳过责任归属规则：非行动事项'],
                shouldContinue: true
            };
        }
        if (decision.canDoInTwoMinutes === true) {
            return {
                ruleName: this.name,
                decision: {
                    isMyResponsibility: true
                },
                confidence: constants_1.CONFIDENCE_THRESHOLDS.HIGH,
                reasoning: ['快速行动默认自己执行'],
                shouldContinue: true
            };
        }
        // 分析任务描述
        const analysis = this.analyzeOwnership(item.description);
        // 判断责任归属
        const isMyResponsibility = this.determineOwnership(analysis, item);
        // 构建推理过程
        const reasoning = this.buildReasoning(analysis, isMyResponsibility);
        // 计算置信度
        const confidence = this.calculateConfidence(analysis);
        // 如果需要委派，设置目标清单
        let targetList;
        if (!isMyResponsibility) {
            targetList = 'waiting';
        }
        return {
            ruleName: this.name,
            decision: {
                isMyResponsibility,
                targetList: targetList
            },
            confidence,
            reasoning,
            shouldContinue: isMyResponsibility // 如果需要委派，可以结束决策
        };
    }
    /**
     * 分析任务的责任归属
     */
    analyzeOwnership(description) {
        const text = description.toLowerCase();
        const analysis = {
            hasDelegationIndicators: false,
            hasPersonIndicators: false,
            hasSelfIndicators: false,
            delegationKeywords: [],
            personKeywords: [],
            selfKeywords: [],
            explicitPerson: null
        };
        // 检查委派指示词
        for (const indicator of constants_1.DELEGATION_KEYWORDS.DELEGATION_INDICATORS) {
            if (text.includes(indicator)) {
                analysis.hasDelegationIndicators = true;
                analysis.delegationKeywords.push(indicator);
            }
        }
        // 检查人员指示词
        for (const person of constants_1.DELEGATION_KEYWORDS.PERSON_INDICATORS) {
            if (text.includes(person)) {
                analysis.hasPersonIndicators = true;
                analysis.personKeywords.push(person);
            }
        }
        // 检查自己执行的指示词
        const selfIndicators = ['我', '自己', '亲自', '独立', '单独'];
        for (const self of selfIndicators) {
            if (text.includes(self)) {
                analysis.hasSelfIndicators = true;
                analysis.selfKeywords.push(self);
            }
        }
        // 尝试提取具体的人名或角色
        analysis.explicitPerson = this.extractExplicitPerson(text);
        return analysis;
    }
    /**
     * 提取明确的人员信息
     */
    extractExplicitPerson(text) {
        // 匹配常见的人员表达模式
        const patterns = [
            /让(.{1,10})(?:帮忙|协助|处理|负责|完成)/,
            /请(.{1,10})(?:帮忙|协助|处理|负责|完成)/,
            /找(.{1,10})(?:帮忙|协助|处理|负责|完成)/,
            /交给(.{1,10})(?:处理|负责|完成)/,
            /委托(.{1,10})(?:处理|负责|完成)/,
            /安排(.{1,10})(?:处理|负责|完成)/
        ];
        for (const pattern of patterns) {
            const match = text.match(pattern);
            if (match && match[1]) {
                const person = match[1].trim();
                // 过滤掉一些非人员的词汇
                if (!['我', '自己', '大家', '所有人', '团队'].includes(person)) {
                    return person;
                }
            }
        }
        return null;
    }
    /**
     * 判断责任归属
     */
    determineOwnership(analysis, item) {
        // 强委派指示器
        if (analysis.hasDelegationIndicators && (analysis.hasPersonIndicators || analysis.explicitPerson)) {
            return false;
        }
        // 明确的自己执行指示器
        if (analysis.hasSelfIndicators) {
            return true;
        }
        // 检查任务类型是否适合委派
        if (this.isTypicallyDelegated(item.description)) {
            return false;
        }
        // 检查任务类型是否必须自己执行
        if (this.isTypicallyPersonal(item.description)) {
            return true;
        }
        // 默认情况下，假设是自己的责任
        return true;
    }
    /**
     * 检查是否为典型的委派任务
     */
    isTypicallyDelegated(description) {
        const text = description.toLowerCase();
        const delegationTasks = [
            '预约', '安排会议', '联系客户', '发送邮件给', '通知',
            '收集资料', '整理文档', '打印', '复印', '邮寄',
            '采购', '订购', '购买', '报销', '审批'
        ];
        return delegationTasks.some(task => text.includes(task));
    }
    /**
     * 检查是否为典型的个人任务
     */
    isTypicallyPersonal(description) {
        const text = description.toLowerCase();
        const personalTasks = [
            '学习', '阅读', '思考', '决策', '规划', '设计',
            '写作', '创作', '分析', '评估', '审核', '检查',
            '个人', '私人', '家庭', '健康', '锻炼'
        ];
        return personalTasks.some(task => text.includes(task));
    }
    /**
     * 构建推理过程
     */
    buildReasoning(analysis, isMyResponsibility) {
        const reasoning = [];
        if (isMyResponsibility) {
            reasoning.push('判断为自己负责的任务');
            if (analysis.hasSelfIndicators) {
                reasoning.push(`检测到自己执行指示词: ${analysis.selfKeywords.join(', ')}`);
            }
            if (!analysis.hasDelegationIndicators) {
                reasoning.push('未检测到委派指示词');
            }
        }
        else {
            reasoning.push('判断为需要委派的任务');
            if (analysis.hasDelegationIndicators) {
                reasoning.push(`检测到委派指示词: ${analysis.delegationKeywords.join(', ')}`);
            }
            if (analysis.hasPersonIndicators) {
                reasoning.push(`检测到人员指示词: ${analysis.personKeywords.join(', ')}`);
            }
            if (analysis.explicitPerson) {
                reasoning.push(`识别到具体人员: ${analysis.explicitPerson}`);
            }
            reasoning.push('建议移入等待清单，设置跟进提醒');
        }
        return reasoning;
    }
    /**
     * 计算置信度
     */
    calculateConfidence(analysis) {
        let confidence = constants_1.CONFIDENCE_THRESHOLDS.MEDIUM;
        // 有明确的委派指示器，提高置信度
        if (analysis.hasDelegationIndicators && analysis.hasPersonIndicators) {
            confidence += 0.3;
        }
        // 有明确的人员信息，提高置信度
        if (analysis.explicitPerson) {
            confidence += 0.2;
        }
        // 有明确的自己执行指示器，提高置信度
        if (analysis.hasSelfIndicators) {
            confidence += 0.2;
        }
        // 既有委派又有自己执行的指示器，降低置信度（矛盾）
        if (analysis.hasDelegationIndicators && analysis.hasSelfIndicators) {
            confidence -= 0.3;
        }
        return Math.min(1.0, Math.max(constants_1.CONFIDENCE_THRESHOLDS.LOW, confidence));
    }
}
exports.OwnershipRule = OwnershipRule;
//# sourceMappingURL=ownership-rule.js.map