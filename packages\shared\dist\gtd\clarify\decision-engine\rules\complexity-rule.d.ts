import { DecisionRule, RuleResult } from '../../types';
import { DecisionContext } from '../decision-context';
/**
 * 复杂度规则
 * 判断任务是单一行动还是需要分解的多步骤项目
 */
export declare class ComplexityRule implements DecisionRule {
    readonly name = "ComplexityRule";
    readonly priority: 4;
    evaluate(context: DecisionContext): Promise<RuleResult>;
    /**
     * 分析任务复杂度
     */
    private analyzeComplexity;
    /**
     * 计算描述中的步骤数量
     */
    private countSteps;
    /**
     * 检查是否有子任务指示器
     */
    private hasSubTaskIndicators;
    /**
     * 判断是否为单一行动
     */
    private determineSingleAction;
    /**
     * 检查是否为典型的单一行动
     */
    private isTypicalSingleAction;
    /**
     * 检查是否为典型的项目
     */
    private isTypicalProject;
    /**
     * 构建推理过程
     */
    private buildReasoning;
    /**
     * 计算置信度
     */
    private calculateConfidence;
}
//# sourceMappingURL=complexity-rule.d.ts.map