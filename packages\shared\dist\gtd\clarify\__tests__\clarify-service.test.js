"use strict";
// 明晰服务单元测试
Object.defineProperty(exports, "__esModule", { value: true });
const clarify_service_1 = require("../clarify-service");
const decision_tree_1 = require("../decision-engine/decision-tree");
const actionability_rule_1 = require("../decision-engine/rules/actionability-rule");
// Mock repositories
const mockInboxRepository = {
    findById: jest.fn(),
    update: jest.fn(),
    create: jest.fn(),
    delete: jest.fn(),
    findAll: jest.fn()
};
const mockTaskRepository = {
    findById: jest.fn(),
    update: jest.fn(),
    create: jest.fn(),
    delete: jest.fn(),
    findAll: jest.fn()
};
const mockProjectRepository = {
    findById: jest.fn(),
    update: jest.fn(),
    create: jest.fn(),
    delete: jest.fn(),
    findAll: jest.fn()
};
describe('ClarifyService', () => {
    let clarifyService;
    let decisionEngine;
    let mockLogger;
    beforeEach(() => {
        jest.clearAllMocks();
        mockLogger = jest.fn();
        decisionEngine = new decision_tree_1.DecisionTree(mockLogger);
        decisionEngine.addRule(new actionability_rule_1.ActionabilityRule());
        clarifyService = new clarify_service_1.ClarifyService(mockInboxRepository, mockTaskRepository, mockProjectRepository, decisionEngine, mockLogger);
    });
    describe('clarifyItem', () => {
        it('应该成功明晰单个收集箱条目', async () => {
            const mockItem = {
                id: 'test-item-1',
                description: '打电话给客户',
                source: 'manual',
                status: 'captured',
                createdAt: new Date(),
                updatedAt: new Date(),
                version: 1
            };
            const mockTask = {
                id: 'task-1',
                title: '打电话给客户',
                status: 'active',
                priority: 'medium',
                category: 'work',
                contexts: ['@电话'],
                createdAt: new Date(),
                updatedAt: new Date(),
                version: 1
            };
            const updatedItem = { ...mockItem, status: 'clarified' };
            mockInboxRepository.findById.mockResolvedValue(mockItem);
            mockInboxRepository.update.mockResolvedValue(updatedItem);
            mockTaskRepository.create.mockResolvedValue(mockTask);
            const result = await clarifyService.clarifyItem('test-item-1');
            expect(result).toBeDefined();
            expect(result.decision).toBeDefined();
            expect(result.createdItems).toHaveLength(1);
            expect(result.createdItems[0].type).toBe('task');
            expect(result.updatedInboxItem.status).toBe('clarified');
            expect(result.processingTime).toBeGreaterThan(0);
            // 验证仓库调用
            expect(mockInboxRepository.findById).toHaveBeenCalledWith('test-item-1');
            expect(mockInboxRepository.update).toHaveBeenCalledWith('test-item-1', { status: 'processing' });
            expect(mockInboxRepository.update).toHaveBeenCalledWith('test-item-1', { status: 'clarified' });
            expect(mockTaskRepository.create).toHaveBeenCalled();
        });
        it('应该处理项目创建', async () => {
            const mockItem = {
                id: 'test-item-2',
                description: '开发新的客户管理系统',
                estimatedMinutes: 240,
                source: 'manual',
                status: 'captured',
                createdAt: new Date(),
                updatedAt: new Date(),
                version: 1
            };
            const mockProject = {
                id: 'project-1',
                title: '开发新的客户管理系统',
                status: 'active',
                category: 'work',
                expectedOutcome: '完成项目目标',
                progress: 0,
                createdAt: new Date(),
                updatedAt: new Date(),
                version: 1
            };
            const mockNextAction = {
                id: 'task-2',
                title: '开发新的客户管理系统 - 下一步行动',
                status: 'active',
                priority: 'medium',
                category: 'work',
                contexts: ['@规划'],
                projectId: 'project-1',
                estimatedMinutes: 15,
                createdAt: new Date(),
                updatedAt: new Date(),
                version: 1
            };
            mockInboxRepository.findById.mockResolvedValue(mockItem);
            mockInboxRepository.update.mockResolvedValue({ ...mockItem, status: 'clarified' });
            mockProjectRepository.create.mockResolvedValue(mockProject);
            mockTaskRepository.create.mockResolvedValue(mockNextAction);
            // 模拟决策引擎返回项目决策
            const mockDecisionEngine = {
                processItem: jest.fn().mockResolvedValue({
                    isActionable: true,
                    isSingleAction: false,
                    targetList: 'projects',
                    reasoning: ['判断为多步骤项目'],
                    confidence: 0.8,
                    appliedRules: ['ActionabilityRule', 'ComplexityRule']
                })
            };
            const serviceWithMockEngine = new clarify_service_1.ClarifyService(mockInboxRepository, mockTaskRepository, mockProjectRepository, mockDecisionEngine, mockLogger);
            const result = await serviceWithMockEngine.clarifyItem('test-item-2');
            expect(result.createdItems).toHaveLength(2);
            expect(result.createdItems[0].type).toBe('project');
            expect(result.createdItems[1].type).toBe('task');
            expect(result.decision.targetList).toBe('projects');
        });
        it('应该处理垃圾箱条目', async () => {
            const mockItem = {
                id: 'test-item-3',
                description: '删除这个无用信息',
                source: 'manual',
                status: 'captured',
                createdAt: new Date(),
                updatedAt: new Date(),
                version: 1
            };
            mockInboxRepository.findById.mockResolvedValue(mockItem);
            mockInboxRepository.update.mockResolvedValue({ ...mockItem, status: 'clarified' });
            // 模拟决策引擎返回垃圾箱决策
            const mockDecisionEngine = {
                processItem: jest.fn().mockResolvedValue({
                    isActionable: false,
                    targetList: 'trash',
                    reasoning: ['判断为垃圾信息'],
                    confidence: 0.9,
                    appliedRules: ['ActionabilityRule']
                })
            };
            const serviceWithMockEngine = new clarify_service_1.ClarifyService(mockInboxRepository, mockTaskRepository, mockProjectRepository, mockDecisionEngine, mockLogger);
            const result = await serviceWithMockEngine.clarifyItem('test-item-3');
            expect(result.createdItems).toHaveLength(0); // 垃圾箱不创建新条目
            expect(result.decision.targetList).toBe('trash');
        });
        it('应该处理收集箱条目不存在的情况', async () => {
            mockInboxRepository.findById.mockResolvedValue(null);
            await expect(clarifyService.clarifyItem('non-existent')).rejects.toThrow('收集箱条目不存在: non-existent');
        });
        it('应该处理决策引擎错误', async () => {
            const mockItem = {
                id: 'test-item-4',
                description: '测试错误',
                source: 'manual',
                status: 'captured',
                createdAt: new Date(),
                updatedAt: new Date(),
                version: 1
            };
            mockInboxRepository.findById.mockResolvedValue(mockItem);
            mockInboxRepository.update.mockResolvedValue(mockItem);
            const errorEngine = {
                processItem: jest.fn().mockRejectedValue(new Error('决策引擎错误'))
            };
            const serviceWithErrorEngine = new clarify_service_1.ClarifyService(mockInboxRepository, mockTaskRepository, mockProjectRepository, errorEngine, mockLogger);
            await expect(serviceWithErrorEngine.clarifyItem('test-item-4')).rejects.toThrow('决策引擎错误');
            // 应该恢复收集箱状态
            expect(mockInboxRepository.update).toHaveBeenCalledWith('test-item-4', { status: 'captured' });
        });
    });
    describe('batchClarify', () => {
        it('应该成功批量明晰多个条目', async () => {
            const mockItems = [
                {
                    id: 'item-1',
                    description: '任务1',
                    source: 'manual',
                    status: 'captured',
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    version: 1
                },
                {
                    id: 'item-2',
                    description: '任务2',
                    source: 'manual',
                    status: 'captured',
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    version: 1
                }
            ];
            const mockTask = {
                id: 'task-1',
                title: '任务',
                status: 'active',
                priority: 'medium',
                category: 'work',
                contexts: [],
                createdAt: new Date(),
                updatedAt: new Date(),
                version: 1
            };
            mockInboxRepository.findById.mockImplementation((id) => Promise.resolve(mockItems.find(item => item.id === id)));
            mockInboxRepository.update.mockImplementation((id, updates) => Promise.resolve({ ...mockItems.find(item => item.id === id), ...updates }));
            mockTaskRepository.create.mockResolvedValue(mockTask);
            const result = await clarifyService.batchClarify(['item-1', 'item-2']);
            expect(result.results).toHaveLength(2);
            expect(result.errors).toHaveLength(0);
            expect(result.summary.total).toBe(2);
            expect(result.summary.successful).toBe(2);
            expect(result.summary.failed).toBe(0);
        });
        it('应该处理批量处理中的错误', async () => {
            mockInboxRepository.findById.mockImplementation((id) => {
                if (id === 'error-item') {
                    return Promise.reject(new Error('数据库错误'));
                }
                return Promise.resolve({
                    id,
                    description: '正常任务',
                    source: 'manual',
                    status: 'captured',
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    version: 1
                });
            });
            mockInboxRepository.update.mockResolvedValue({});
            mockTaskRepository.create.mockResolvedValue({});
            const result = await clarifyService.batchClarify(['normal-item', 'error-item'], {
                stopOnError: false
            });
            expect(result.results).toHaveLength(1);
            expect(result.errors).toHaveLength(1);
            expect(result.errors[0].itemId).toBe('error-item');
            expect(result.summary.successful).toBe(1);
            expect(result.summary.failed).toBe(1);
        });
        it('应该在stopOnError为true时停止处理', async () => {
            mockInboxRepository.findById.mockImplementation((id) => {
                if (id === 'error-item') {
                    return Promise.reject(new Error('数据库错误'));
                }
                return Promise.resolve({
                    id,
                    description: '正常任务',
                    source: 'manual',
                    status: 'captured',
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    version: 1
                });
            });
            const result = await clarifyService.batchClarify(['error-item', 'normal-item'], {
                stopOnError: true
            });
            expect(result.results).toHaveLength(0);
            expect(result.errors).toHaveLength(1);
        });
    });
    describe('事件系统', () => {
        it('应该触发明晰开始和完成事件', async () => {
            const mockItem = {
                id: 'test-item-5',
                description: '测试事件',
                source: 'manual',
                status: 'captured',
                createdAt: new Date(),
                updatedAt: new Date(),
                version: 1
            };
            mockInboxRepository.findById.mockResolvedValue(mockItem);
            mockInboxRepository.update.mockResolvedValue({ ...mockItem, status: 'clarified' });
            mockTaskRepository.create.mockResolvedValue({});
            const startListener = jest.fn();
            const completeListener = jest.fn();
            clarifyService.on('clarify_started', startListener);
            clarifyService.on('clarify_completed', completeListener);
            await clarifyService.clarifyItem('test-item-5');
            expect(startListener).toHaveBeenCalledWith({ itemId: 'test-item-5' });
            expect(completeListener).toHaveBeenCalledWith(expect.objectContaining({
                itemId: 'test-item-5',
                result: expect.any(Object)
            }));
        });
        it('应该触发明晰失败事件', async () => {
            mockInboxRepository.findById.mockRejectedValue(new Error('数据库错误'));
            const failListener = jest.fn();
            clarifyService.on('clarify_failed', failListener);
            await expect(clarifyService.clarifyItem('test-item-6')).rejects.toThrow();
            expect(failListener).toHaveBeenCalledWith(expect.objectContaining({
                itemId: 'test-item-6',
                error: '数据库错误'
            }));
        });
    });
});
//# sourceMappingURL=clarify-service.test.js.map