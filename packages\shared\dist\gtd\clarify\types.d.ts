import { InboxItem, TaskCategory } from '../../types';
/**
 * 目标清单类型
 */
export type TargetList = 'trash' | 'reference' | 'incubator' | 'next-actions' | 'waiting' | 'projects' | 'calendar';
/**
 * 明晰决策结果
 */
export interface ClarifyDecision {
    isActionable: boolean;
    canDoInTwoMinutes?: boolean;
    isMyResponsibility?: boolean;
    isSingleAction?: boolean;
    hasSpecificTime?: boolean;
    targetList: TargetList;
    quickActionSuggestion?: boolean;
    reasoning: string[];
    confidence: number;
    appliedRules: string[];
}
/**
 * 明晰处理结果
 */
export interface ClarifyResult {
    decision: ClarifyDecision;
    createdItems: ClarifiedItem[];
    updatedInboxItem: InboxItem;
    processingTime: number;
}
/**
 * 明晰后创建的条目
 */
export interface ClarifiedItem {
    type: 'task' | 'project' | 'waiting' | 'reference' | 'calendar';
    data: any;
    targetList: TargetList;
    id: string;
}
/**
 * 等待清单条目
 */
export interface WaitingItem {
    id: string;
    description: string;
    delegatedTo: string;
    expectedDate?: Date;
    followUpDate: Date;
    context?: string;
    relatedProjectId?: string;
    notes?: string;
    createdAt: Date;
    updatedAt: Date;
}
/**
 * 参考资料条目
 */
export interface ReferenceItem {
    id: string;
    title: string;
    content: string;
    tags: string[];
    category: string;
    relatedProjectId?: string;
    url?: string;
    attachments?: string[];
    createdAt: Date;
    updatedAt: Date;
}
/**
 * 孵化池条目
 */
export interface IncubatorItem {
    id: string;
    description: string;
    type: 'someday-maybe' | 'future-project';
    category?: TaskCategory;
    reviewDate?: Date;
    notes?: string;
    tags: string[];
    createdAt: Date;
    updatedAt: Date;
}
/**
 * 日历条目
 */
export interface CalendarItem {
    id: string;
    title: string;
    description?: string;
    startTime: Date;
    endTime?: Date;
    location?: string;
    attendees?: string[];
    isAllDay: boolean;
    category?: TaskCategory;
    createdAt: Date;
    updatedAt: Date;
}
/**
 * 决策上下文接口
 */
export interface IDecisionContext {
    item: InboxItem;
    userPreferences?: UserPreferences;
    currentWorkload?: WorkloadInfo;
    timeContext?: TimeContext;
}
/**
 * 用户偏好设置
 */
export interface UserPreferences {
    defaultCategory: TaskCategory;
    twoMinuteThreshold: number;
    autoAssignContexts: boolean;
    preferredContexts: string[];
}
/**
 * 工作负载信息
 */
export interface WorkloadInfo {
    activeTasks: number;
    activeProjects: number;
    upcomingDeadlines: number;
    availableTime: number;
}
/**
 * 时间上下文
 */
export interface TimeContext {
    currentTime: Date;
    workingHours: {
        start: string;
        end: string;
    };
    isWorkingTime: boolean;
    timeZone: string;
}
/**
 * 决策规则接口
 */
export interface DecisionRule {
    name: string;
    priority: number;
    evaluate(context: IDecisionContext): Promise<RuleResult>;
}
/**
 * 规则执行结果
 */
export interface RuleResult {
    ruleName: string;
    decision?: Partial<ClarifyDecision>;
    confidence: number;
    reasoning: string[];
    shouldContinue: boolean;
}
/**
 * 批量明晰选项
 */
export interface BatchClarifyOptions {
    maxConcurrent?: number;
    stopOnError?: boolean;
    userInteraction?: boolean;
}
/**
 * 批量明晰结果
 */
export interface BatchClarifyResult {
    results: ClarifyResult[];
    errors: Array<{
        itemId: string;
        error: string;
    }>;
    summary: {
        total: number;
        successful: number;
        failed: number;
        processingTime: number;
    };
}
//# sourceMappingURL=types.d.ts.map