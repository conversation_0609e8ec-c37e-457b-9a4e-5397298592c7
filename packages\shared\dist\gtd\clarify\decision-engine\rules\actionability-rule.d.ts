import { DecisionRule, RuleResult } from '../../types';
import { DecisionContext } from '../decision-context';
/**
 * 可行动性规则
 * 判断收集箱条目是否为可行动的事项
 * 这是GTD明晰流程的第一个关键决策点
 */
export declare class ActionabilityRule implements DecisionRule {
    readonly name = "ActionabilityRule";
    readonly priority: 1;
    evaluate(context: DecisionContext): Promise<RuleResult>;
    /**
     * 分析文本内容
     */
    private analyzeText;
    /**
     * 判断可行动性
     */
    private determineActionability;
    /**
     * 对非行动项目进行分类
     */
    private categorizeNonActionable;
    /**
     * 判断是否为垃圾信息
     */
    private isTrash;
    /**
     * 判断是否为将来/也许
     */
    private isSomedayMaybe;
    /**
     * 构建推理过程
     */
    private buildReasoning;
    /**
     * 计算置信度
     */
    private calculateConfidence;
    /**
     * 获取目标清单名称
     */
    private getTargetListName;
}
//# sourceMappingURL=actionability-rule.d.ts.map