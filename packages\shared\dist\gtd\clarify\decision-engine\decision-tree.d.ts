import { DecisionRule, ClarifyDecision, IDecisionContext } from '../types';
import { InboxItem } from '../../../types';
/**
 * 决策树类
 * 负责协调和执行整个GTD明晰决策流程
 */
export declare class DecisionTree {
    private rules;
    private logger?;
    constructor(logger?: (level: string, message: string, data?: any) => void);
    /**
     * 添加决策规则
     */
    addRule(rule: DecisionRule): void;
    /**
     * 批量添加规则
     */
    addRules(rules: DecisionRule[]): void;
    /**
     * 移除规则
     */
    removeRule(ruleName: string): boolean;
    /**
     * 获取所有规则
     */
    getRules(): DecisionRule[];
    /**
     * 处理单个收集箱条目
     */
    processItem(item: InboxItem, context?: Partial<IDecisionContext>): Promise<ClarifyDecision>;
    /**
     * 批量处理收集箱条目
     */
    processItems(items: InboxItem[], context?: Partial<IDecisionContext>): Promise<ClarifyDecision[]>;
    /**
     * 执行所有决策规则
     */
    private executeRules;
    /**
     * 处理规则执行结果
     */
    private processRuleResult;
    /**
     * 验证决策结果
     */
    private validateDecision;
    /**
     * 验证决策逻辑一致性
     */
    private validateDecisionLogic;
    /**
     * 日志记录
     */
    private log;
}
//# sourceMappingURL=decision-tree.d.ts.map