{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM"], "module": "CommonJS", "moduleResolution": "node", "outDir": "./dist", "rootDir": "./src", "composite": true, "declaration": true, "declarationMap": true, "sourceMap": true, "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}