{"name": "<PERSON><PERSON><PERSON>", "version": "2.7.1", "main": "rimraf.js", "description": "A deep deletion module for node (like `rm -rf`)", "author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "license": "ISC", "repository": "git://github.com/isaacs/rimraf.git", "scripts": {"preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "test": "tap test/*.js"}, "bin": "./bin.js", "dependencies": {"glob": "^7.1.3"}, "files": ["LICENSE", "README.md", "bin.js", "rimraf.js"], "devDependencies": {"mkdirp": "^0.5.1", "tap": "^12.1.1"}}