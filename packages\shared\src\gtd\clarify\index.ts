// GTD 明晰模块主入口

// MVP版本：用户主导的决策流程
export { ClarifyServiceMVP } from './clarify-service-mvp';
export { ClarifyWizard } from './wizard/clarify-wizard';
export type {
  WizardSession,
  WizardAnswers,
  StepQuestion,
  StepOption,
  ClarifyStep,
  ClarificationResult
} from './wizard/clarify-wizard';

// Pro版本：AI辅助的决策流程
export { ClarifyService } from './clarify-service';
export { DecisionEngineFactory } from './decision-engine';

// 共享类型和常量（避免重复导出）
export type {
  ClarifyDecision,
  TargetList,
  UserPreferences,
  WorkloadInfo,
  TimeContext,
  DecisionRule,
  RuleResult,
  BatchClarifyOptions,
  BatchClarifyResult
} from './types';

export {
  RULE_PRIORITIES,
  CONFIDENCE_THRESHOLDS,
  TIME_CONSTANTS,
  DEFAULT_CONFIG
} from './constants';

// 便捷导入
import { ClarifyServiceMVP } from './clarify-service-mvp';
import { ClarifyService } from './clarify-service';
import { DecisionEngineFactory } from './decision-engine';
import { InboxRepository, TaskRepository, ProjectRepository } from '../../types/database';

/**
 * 明晰模块工厂类
 * 提供便捷的服务创建方法，支持MVP和Pro两个版本
 */
export class ClarifyModuleFactory {
  /**
   * 创建MVP版本明晰服务（用户主导决策）
   * 推荐用于初期产品和用户学习GTD流程
   */
  static createMVPService(
    inboxRepository: InboxRepository,
    taskRepository: TaskRepository,
    projectRepository: ProjectRepository,
    logger?: (level: string, message: string, data?: any) => void
  ): ClarifyServiceMVP {
    return new ClarifyServiceMVP(
      inboxRepository,
      taskRepository,
      projectRepository,
      logger
    );
  }
  /**
   * 创建Pro版本明晰服务（AI辅助决策）
   * 包含完整的GTD决策引擎，适合高级用户
   */
  static createProService(
    inboxRepository: InboxRepository,
    taskRepository: TaskRepository,
    projectRepository: ProjectRepository,
    logger?: (level: string, message: string, data?: any) => void
  ): ClarifyService {
    const decisionEngine = DecisionEngineFactory.createStandardEngine(logger);
    
    return new ClarifyService(
      inboxRepository,
      taskRepository,
      projectRepository,
      decisionEngine,
      logger
    );
  }

  /**
   * 获取支持的目标清单类型
   */
  static getSupportedTargetLists(): string[] {
    return [
      'trash',
      'reference', 
      'incubator',
      'next-actions',
      'waiting',
      'projects',
      'calendar'
    ];
  }

  /**
   * 获取决策规则信息
   */
  static getDecisionRulesInfo() {
    return DecisionEngineFactory.getRuleInfo();
  }

  /**
   * 验证明晰服务配置
   */
  static validateConfiguration(config: {
    inboxRepository: any;
    taskRepository: any;
    projectRepository: any;
  }): boolean {
    const { inboxRepository, taskRepository, projectRepository } = config;
    
    // 检查必需的仓库方法
    const requiredMethods = ['findById', 'create', 'update'];
    
    for (const method of requiredMethods) {
      if (typeof inboxRepository[method] !== 'function') {
        throw new Error(`InboxRepository 缺少方法: ${method}`);
      }
      if (typeof taskRepository[method] !== 'function') {
        throw new Error(`TaskRepository 缺少方法: ${method}`);
      }
      if (typeof projectRepository[method] !== 'function') {
        throw new Error(`ProjectRepository 缺少方法: ${method}`);
      }
    }
    
    return true;
  }
}

/**
 * 明晰服务健康检查
 */
export class ClarifyHealthChecker {
  constructor(private clarifyService: ClarifyService) {}

  /**
   * 执行健康检查
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    checks: Array<{
      name: string;
      status: 'pass' | 'fail';
      message?: string;
    }>;
  }> {
    const checks = [];
    let overallStatus: 'healthy' | 'unhealthy' = 'healthy';

    // 检查决策引擎
    try {
      // 这里可以添加具体的健康检查逻辑
      checks.push({
        name: 'decision_engine',
        status: 'pass' as const
      });
    } catch (error) {
      checks.push({
        name: 'decision_engine',
        status: 'fail' as const,
        message: error instanceof Error ? error.message : String(error)
      });
      overallStatus = 'unhealthy';
    }

    // 检查数据库连接
    try {
      // 这里可以添加数据库连接检查
      checks.push({
        name: 'database_connection',
        status: 'pass' as const
      });
    } catch (error) {
      checks.push({
        name: 'database_connection',
        status: 'fail' as const,
        message: error instanceof Error ? error.message : String(error)
      });
      overallStatus = 'unhealthy';
    }

    return {
      status: overallStatus,
      checks
    };
  }
}

/**
 * 明晰统计信息收集器
 */
export class ClarifyStatsCollector {
  private stats = {
    totalClarified: 0,
    byTargetList: new Map<string, number>(),
    averageProcessingTime: 0,
    totalProcessingTime: 0,
    errorCount: 0
  };

  /**
   * 记录明晰结果
   */
  recordClarification(result: any): void {
    this.stats.totalClarified++;
    this.stats.totalProcessingTime += result.processingTime;
    this.stats.averageProcessingTime = this.stats.totalProcessingTime / this.stats.totalClarified;

    const targetList = result.decision.targetList;
    const currentCount = this.stats.byTargetList.get(targetList) || 0;
    this.stats.byTargetList.set(targetList, currentCount + 1);
  }

  /**
   * 记录错误
   */
  recordError(): void {
    this.stats.errorCount++;
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      ...this.stats,
      byTargetList: Object.fromEntries(this.stats.byTargetList),
      successRate: this.stats.totalClarified / (this.stats.totalClarified + this.stats.errorCount)
    };
  }

  /**
   * 重置统计信息
   */
  reset(): void {
    this.stats = {
      totalClarified: 0,
      byTargetList: new Map(),
      averageProcessingTime: 0,
      totalProcessingTime: 0,
      errorCount: 0
    };
  }
}
