import { ClarifyServiceMVP } from '../clarify-service-mvp';
/**
 * MVP版本使用示例
 * 展示用户主导的逐步决策流程
 */
export declare class MVPUsageExample {
    private clarifyService;
    constructor(clarifyService: ClarifyServiceMVP);
    /**
     * 示例：明晰 "让小王帮忙整理下周会议的资料"
     */
    demonstrateUserGuidedClarification(): Promise<import("../clarify-service-mvp").ClarifyResultMVP>;
    /**
     * 示例：明晰 "写下周的工作报告"
     */
    demonstrateTaskClarification(): Promise<import("../clarify-service-mvp").ClarifyResultMVP>;
    /**
     * 示例：明晰 "组织公司年会"
     */
    demonstrateProjectClarification(): Promise<import("../clarify-service-mvp").ClarifyResultMVP>;
    /**
     * 展示完整的向导界面模拟
     */
    simulateWizardUI(): Promise<import("../clarify-service-mvp").ClarifyResultMVP>;
}
/**
 * 运行所有示例
 */
export declare function runMVPExamples(clarifyService: ClarifyServiceMVP): Promise<void>;
//# sourceMappingURL=mvp-usage-example.d.ts.map