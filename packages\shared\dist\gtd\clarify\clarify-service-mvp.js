"use strict";
// GTD 明晰服务 - MVP版本：用户主导的决策流程
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClarifyServiceMVP = void 0;
const clarify_wizard_1 = require("./wizard/clarify-wizard");
/**
 * MVP版本的明晰服务
 * 提供用户主导的逐步决策流程
 */
class ClarifyServiceMVP {
    inboxRepository;
    taskRepository;
    projectRepository;
    logger;
    wizard;
    constructor(inboxRepository, taskRepository, projectRepository, logger) {
        this.inboxRepository = inboxRepository;
        this.taskRepository = taskRepository;
        this.projectRepository = projectRepository;
        this.logger = logger;
        this.wizard = new clarify_wizard_1.ClarifyWizard();
    }
    // === 向导流程管理 ===
    /**
     * 开始明晰流程
     */
    async startClarification(itemId) {
        const item = await this.inboxRepository.findById(itemId);
        if (!item) {
            throw new Error(`收集箱条目不存在: ${itemId}`);
        }
        // 更新状态为处理中
        await this.inboxRepository.update(itemId, { status: 'processing' });
        this.log('info', '开始明晰向导', { itemId, description: item.description });
        return await this.wizard.startClarification(item);
    }
    /**
     * 获取当前步骤问题
     */
    getCurrentQuestion(sessionId) {
        return this.wizard.getCurrentQuestion(sessionId);
    }
    /**
     * 提交答案
     */
    async submitAnswer(sessionId, answer, additionalData) {
        this.log('debug', '提交答案', { sessionId, answer, additionalData });
        return await this.wizard.submitAnswer(sessionId, answer, additionalData);
    }
    /**
     * 完成明晰并创建目标条目
     */
    async completeClarification(sessionId) {
        const result = this.wizard.getClarificationResult(sessionId);
        if (!result) {
            throw new Error(`明晰会话未完成或不存在: ${sessionId}`);
        }
        // 获取原始收集箱条目
        const session = this.wizard.sessions.get(sessionId);
        const item = await this.inboxRepository.findById(session.itemId);
        if (!item) {
            throw new Error(`收集箱条目不存在: ${session.itemId}`);
        }
        // 根据用户决策创建目标条目
        const createdItems = await this.createTargetItems(item, result);
        // 更新收集箱状态
        const updatedItem = await this.inboxRepository.update(session.itemId, {
            status: 'clarified'
        });
        this.log('info', '明晰完成', {
            itemId: session.itemId,
            targetList: result.targetList,
            summary: result.summary
        });
        return {
            sessionId,
            targetList: result.targetList,
            summary: result.summary,
            answers: result.answers,
            createdItems,
            updatedInboxItem: updatedItem
        };
    }
    // === 目标条目创建 ===
    /**
     * 根据用户决策创建目标条目
     */
    async createTargetItems(item, result) {
        const createdItems = [];
        switch (result.targetList) {
            case 'quick-action':
                // 快速行动：创建任务但标记为立即执行
                const quickTask = await this.createQuickActionTask(item, result);
                createdItems.push({
                    type: 'task',
                    data: quickTask,
                    targetList: 'quick-action',
                    id: quickTask.id
                });
                break;
            case 'next-actions':
                // 执行清单：创建普通任务
                const task = await this.createTask(item, result);
                createdItems.push({
                    type: 'task',
                    data: task,
                    targetList: 'next-actions',
                    id: task.id
                });
                break;
            case 'projects':
                // 项目清单：创建项目和下一步行动
                const { project, nextAction } = await this.createProject(item, result);
                createdItems.push({
                    type: 'project',
                    data: project,
                    targetList: 'projects',
                    id: project.id
                });
                if (nextAction) {
                    createdItems.push({
                        type: 'task',
                        data: nextAction,
                        targetList: 'next-actions',
                        id: nextAction.id
                    });
                }
                break;
            case 'waiting':
                // 等待清单：创建等待条目
                const waitingItem = await this.createWaitingItem(item, result);
                createdItems.push({
                    type: 'waiting',
                    data: waitingItem,
                    targetList: 'waiting',
                    id: waitingItem.id
                });
                break;
            case 'calendar':
                // 日历：创建日历条目
                const calendarItem = await this.createCalendarItem(item, result);
                createdItems.push({
                    type: 'calendar',
                    data: calendarItem,
                    targetList: 'calendar',
                    id: calendarItem.id
                });
                break;
            case 'reference':
                // 参考资料：创建参考条目
                const referenceItem = await this.createReferenceItem(item, result);
                createdItems.push({
                    type: 'reference',
                    data: referenceItem,
                    targetList: 'reference',
                    id: referenceItem.id
                });
                break;
            default:
                throw new Error(`未知的目标清单类型: ${result.targetList}`);
        }
        return createdItems;
    }
    /**
     * 创建快速行动任务
     */
    async createQuickActionTask(item, result) {
        const taskData = {
            title: item.description,
            description: '⚡ 快速行动：建议立即执行',
            category: item.category || 'personal',
            priority: 'high', // 快速行动设为高优先级
            contexts: ['@快速行动'],
            estimatedMinutes: 2,
            dueDate: item.deadline
        };
        return await this.taskRepository.create(taskData);
    }
    /**
     * 创建普通任务
     */
    async createTask(item, result) {
        const taskData = {
            title: item.description,
            description: item.expectedOutcome,
            category: item.category || 'personal',
            priority: 'medium',
            contexts: this.extractContexts(item.description),
            estimatedMinutes: item.estimatedMinutes,
            dueDate: item.deadline
        };
        // 如果有日历时间但用户选择不放日历，设置为截止日期
        if (result.answers.calendarTime && !result.answers.hasSpecificTime) {
            taskData.dueDate = result.answers.calendarTime;
        }
        return await this.taskRepository.create(taskData);
    }
    /**
     * 创建项目
     */
    async createProject(item, result) {
        // 创建项目
        const projectData = {
            title: item.description,
            description: `用户明晰创建的项目`,
            category: item.category || 'personal',
            expectedOutcome: result.answers.projectOutcome || item.expectedOutcome || '完成项目目标',
            progress: 0
        };
        const project = await this.projectRepository.create(projectData);
        // 创建下一步行动
        const nextActionData = {
            title: `${item.description} - 下一步行动`,
            description: '请定义这个项目的具体下一步行动',
            category: item.category || 'personal',
            priority: 'medium',
            contexts: ['@规划'],
            projectId: project.id,
            estimatedMinutes: 15
        };
        const nextAction = await this.taskRepository.create(nextActionData);
        return { project, nextAction };
    }
    /**
     * 创建等待条目
     */
    async createWaitingItem(item, result) {
        // TODO: 实现等待清单条目创建
        return {
            id: 'waiting-' + Date.now(),
            description: item.description,
            delegatedTo: result.answers.delegatedTo,
            expectedDate: result.answers.expectedDate,
            followUpDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7天后跟进
            notes: result.answers.notes
        };
    }
    /**
     * 创建日历条目
     */
    async createCalendarItem(item, result) {
        // TODO: 实现日历条目创建
        return {
            id: 'calendar-' + Date.now(),
            title: item.description,
            startTime: result.answers.calendarTime,
            description: item.expectedOutcome,
            notes: result.answers.notes
        };
    }
    /**
     * 创建参考资料
     */
    async createReferenceItem(item, result) {
        // TODO: 实现参考资料创建
        return {
            id: 'reference-' + Date.now(),
            title: item.description,
            content: item.expectedOutcome || '',
            category: item.category || 'general',
            notes: result.answers.notes
        };
    }
    /**
     * 从描述中提取情境（简化版）
     */
    extractContexts(description) {
        const contexts = [];
        const text = description.toLowerCase();
        if (text.includes('电脑') || text.includes('网上') || text.includes('在线')) {
            contexts.push('@电脑');
        }
        if (text.includes('电话') || text.includes('打电话')) {
            contexts.push('@电话');
        }
        if (text.includes('外出') || text.includes('去')) {
            contexts.push('@外出');
        }
        return contexts.length > 0 ? contexts : ['@任何地方'];
    }
    /**
     * 日志记录
     */
    log(level, message, data) {
        if (this.logger) {
            this.logger(level, `[ClarifyServiceMVP] ${message}`, data);
        }
    }
}
exports.ClarifyServiceMVP = ClarifyServiceMVP;
//# sourceMappingURL=clarify-service-mvp.js.map