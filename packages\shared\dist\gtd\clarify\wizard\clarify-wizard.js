"use strict";
// GTD 明晰向导 - MVP版本：用户主导的决策流程
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClarifyWizard = void 0;
/**
 * GTD 明晰向导类
 * 提供用户主导的逐步决策流程
 */
class ClarifyWizard {
    sessions = new Map();
    /**
     * 开始明晰流程
     */
    async startClarification(item) {
        const sessionId = this.generateSessionId();
        const session = {
            id: sessionId,
            itemId: item.id,
            currentStep: 'actionability',
            answers: {},
            isComplete: false,
            createdAt: new Date(),
            updatedAt: new Date()
        };
        this.sessions.set(sessionId, session);
        return session;
    }
    /**
     * 获取当前步骤的问题
     */
    getCurrentQuestion(sessionId) {
        const session = this.sessions.get(sessionId);
        if (!session || session.isComplete) {
            return null;
        }
        return this.getQuestionForStep(session.currentStep);
    }
    /**
     * 提交答案并进入下一步
     */
    async submitAnswer(sessionId, answer, additionalData) {
        const session = this.sessions.get(sessionId);
        if (!session) {
            throw new Error(`会话不存在: ${sessionId}`);
        }
        // 保存答案
        this.saveAnswer(session, answer, additionalData);
        // 确定下一步
        const nextStep = this.determineNextStep(session);
        session.currentStep = nextStep;
        session.updatedAt = new Date();
        // 检查是否完成
        if (nextStep === 'complete') {
            session.isComplete = true;
        }
        return session;
    }
    /**
     * 获取明晰结果
     */
    getClarificationResult(sessionId) {
        const session = this.sessions.get(sessionId);
        if (!session || !session.isComplete) {
            return null;
        }
        return this.buildResult(session);
    }
    /**
     * 获取步骤问题定义
     */
    getQuestionForStep(step) {
        const questions = {
            actionability: {
                step: 'actionability',
                title: '这是一个可以行动的事项吗？',
                description: '判断这个事项是否需要你采取具体行动',
                options: [
                    {
                        value: true,
                        label: '是，需要行动',
                        description: '这是一个需要我做某些具体事情的任务',
                        nextStep: 'time'
                    },
                    {
                        value: false,
                        label: '不是，无需行动',
                        description: '这是信息、想法或将来可能要做的事',
                        nextStep: 'complete'
                    }
                ],
                helpText: '如果你需要做某些具体的事情来处理它，就是可行动的',
                examples: [
                    '✅ 可行动：打电话给客户、写报告、买牛奶',
                    '❌ 不可行动：参考资料、想法记录、将来也许要做的事'
                ]
            },
            time: {
                step: 'time',
                title: '这个任务能在2分钟内完成吗？',
                description: '评估完成这个任务需要多长时间',
                options: [
                    {
                        value: true,
                        label: '是，2分钟内可以完成',
                        description: '立即执行比放入清单管理更高效',
                        nextStep: 'complete'
                    },
                    {
                        value: false,
                        label: '不是，需要更多时间',
                        description: '需要安排时间来完成',
                        nextStep: 'ownership'
                    }
                ],
                helpText: 'GTD的2分钟规则：如果能快速完成，就立即去做',
                examples: [
                    '✅ 2分钟内：发个短信、回复简单邮件、保存文件',
                    '❌ 超过2分钟：写报告、开会、购物'
                ]
            },
            ownership: {
                step: 'ownership',
                title: '这个任务该由你来做吗？',
                description: '确定任务的责任归属',
                options: [
                    {
                        value: true,
                        label: '是，我来做',
                        description: '这个任务需要我亲自完成',
                        nextStep: 'complexity'
                    },
                    {
                        value: false,
                        label: '不是，需要委派',
                        description: '可以委派给其他人完成',
                        nextStep: 'complete',
                        requiresInput: true,
                        inputType: 'person',
                        inputLabel: '委派给谁？'
                    }
                ],
                helpText: '考虑这个任务是否必须由你完成，还是可以委派给别人',
                examples: [
                    '✅ 我来做：需要我的专业技能、决策权限的任务',
                    '❌ 可委派：助理可以处理、下属的职责范围'
                ]
            },
            complexity: {
                step: 'complexity',
                title: '这是一个单一行动吗？',
                description: '判断任务的复杂程度',
                options: [
                    {
                        value: true,
                        label: '是，单一行动',
                        description: '一次就能完成的具体行动',
                        nextStep: 'calendar'
                    },
                    {
                        value: false,
                        label: '不是，多步骤项目',
                        description: '需要多个步骤才能完成的项目',
                        nextStep: 'complete',
                        requiresInput: true,
                        inputType: 'text',
                        inputLabel: '项目的预期成果是什么？'
                    }
                ],
                helpText: '如果需要多个步骤才能完成，就是项目；如果一次行动就能搞定，就是单一任务',
                examples: [
                    '✅ 单一行动：打电话、发邮件、买特定物品',
                    '❌ 多步骤项目：组织活动、开发软件、装修房子'
                ]
            },
            calendar: {
                step: 'calendar',
                title: '这个任务有特定的时间要求吗？',
                description: '确定是否需要在特定时间执行',
                options: [
                    {
                        value: true,
                        label: '是，有特定时间',
                        description: '必须在特定日期/时间执行',
                        nextStep: 'complete',
                        requiresInput: true,
                        inputType: 'date',
                        inputLabel: '什么时间？'
                    },
                    {
                        value: false,
                        label: '不是，时间灵活',
                        description: '可以根据情况灵活安排时间',
                        nextStep: 'complete'
                    }
                ],
                helpText: '只有必须在特定时间做的事情才放入日历，如会议、约会',
                examples: [
                    '✅ 特定时间：会议、约会、航班、生日聚会',
                    '❌ 时间灵活：写报告、购物、锻炼'
                ]
            },
            complete: {
                step: 'complete',
                title: '明晰完成',
                description: '根据你的选择，系统将为你创建相应的任务',
                options: [],
                helpText: '你可以随时回来修改这些设置'
            }
        };
        return questions[step];
    }
    /**
     * 保存用户答案
     */
    saveAnswer(session, answer, additionalData) {
        switch (session.currentStep) {
            case 'actionability':
                session.answers.isActionable = answer;
                break;
            case 'time':
                session.answers.canDoInTwoMinutes = answer;
                break;
            case 'ownership':
                session.answers.isMyResponsibility = answer;
                if (additionalData?.delegatedTo) {
                    session.answers.delegatedTo = additionalData.delegatedTo;
                }
                break;
            case 'complexity':
                session.answers.isSingleAction = answer;
                if (additionalData?.projectOutcome) {
                    session.answers.projectOutcome = additionalData.projectOutcome;
                }
                break;
            case 'calendar':
                session.answers.hasSpecificTime = answer;
                if (additionalData?.calendarTime) {
                    session.answers.calendarTime = additionalData.calendarTime;
                }
                break;
        }
        // 保存备注
        if (additionalData?.notes) {
            session.answers.notes = additionalData.notes;
        }
    }
    /**
     * 确定下一步
     */
    determineNextStep(session) {
        const { answers, currentStep } = session;
        switch (currentStep) {
            case 'actionability':
                return answers.isActionable ? 'time' : 'complete';
            case 'time':
                return answers.canDoInTwoMinutes ? 'complete' : 'ownership';
            case 'ownership':
                return answers.isMyResponsibility ? 'complexity' : 'complete';
            case 'complexity':
                return answers.isSingleAction ? 'calendar' : 'complete';
            case 'calendar':
                return 'complete';
            default:
                return 'complete';
        }
    }
    /**
     * 构建最终结果
     */
    buildResult(session) {
        const { answers } = session;
        // 根据答案确定目标清单
        let targetList;
        if (!answers.isActionable) {
            targetList = 'reference'; // 简化：非行动项目都放参考资料
        }
        else if (answers.canDoInTwoMinutes) {
            targetList = 'quick-action'; // 快速行动
        }
        else if (!answers.isMyResponsibility) {
            targetList = 'waiting'; // 等待清单
        }
        else if (!answers.isSingleAction) {
            targetList = 'projects'; // 项目清单
        }
        else if (answers.hasSpecificTime) {
            targetList = 'calendar'; // 日历
        }
        else {
            targetList = 'next-actions'; // 执行清单
        }
        return {
            sessionId: session.id,
            targetList,
            answers,
            summary: this.generateSummary(answers, targetList)
        };
    }
    /**
     * 生成决策摘要
     */
    generateSummary(answers, targetList) {
        const parts = [];
        if (answers.isActionable === false) {
            parts.push('非行动事项');
        }
        else {
            parts.push('可行动事项');
            if (answers.canDoInTwoMinutes) {
                parts.push('2分钟内可完成');
            }
            else if (!answers.isMyResponsibility) {
                parts.push(`委派给${answers.delegatedTo || '他人'}`);
            }
            else if (!answers.isSingleAction) {
                parts.push('多步骤项目');
            }
            else if (answers.hasSpecificTime) {
                parts.push('有特定时间要求');
            }
            else {
                parts.push('单一行动任务');
            }
        }
        const listNames = {
            'reference': '参考资料',
            'quick-action': '立即执行',
            'waiting': '等待清单',
            'projects': '项目清单',
            'calendar': '日历',
            'next-actions': '执行清单'
        };
        return `${parts.join(' → ')} → ${listNames[targetList]}`;
    }
    /**
     * 生成会话ID
     */
    generateSessionId() {
        return `clarify_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    /**
     * 清理过期会话
     */
    cleanupExpiredSessions(maxAgeHours = 24) {
        const cutoff = new Date(Date.now() - maxAgeHours * 60 * 60 * 1000);
        for (const [sessionId, session] of this.sessions) {
            if (session.createdAt < cutoff) {
                this.sessions.delete(sessionId);
            }
        }
    }
}
exports.ClarifyWizard = ClarifyWizard;
//# sourceMappingURL=clarify-wizard.js.map