// 决策规则单元测试

import { ActionabilityRule } from '../decision-engine/rules/actionability-rule';
import { TimeRule } from '../decision-engine/rules/time-rule';
import { OwnershipRule } from '../decision-engine/rules/ownership-rule';
import { ComplexityRule } from '../decision-engine/rules/complexity-rule';
import { CalendarRule } from '../decision-engine/rules/calendar-rule';
import { DecisionContext } from '../decision-engine/decision-context';
import { InboxItem } from '../../../types';

describe('Decision Rules', () => {
  let mockItem: InboxItem;
  let context: DecisionContext;

  beforeEach(() => {
    mockItem = {
      id: 'test-item',
      description: '测试任务',
      source: 'manual',
      status: 'captured',
      createdAt: new Date(),
      updatedAt: new Date(),
      version: 1
    };
    context = new DecisionContext(mockItem);
  });

  describe('ActionabilityRule', () => {
    let rule: ActionabilityRule;

    beforeEach(() => {
      rule = new ActionabilityRule();
    });

    it('应该识别可行动事项', async () => {
      context = new DecisionContext({
        ...mockItem,
        description: '打电话给客户确认订单'
      });

      const result = await rule.evaluate(context);

      expect(result.decision?.isActionable).toBe(true);
      expect(result.confidence).toBeGreaterThan(0.5);
      expect(result.reasoning).toContain('判断为可行动事项');
    });

    it('应该识别非行动事项', async () => {
      context = new DecisionContext({
        ...mockItem,
        description: '参考资料：项目管理指南'
      });

      const result = await rule.evaluate(context);

      expect(result.decision?.isActionable).toBe(false);
      expect(result.decision?.targetList).toBeDefined();
      expect(result.reasoning).toContain('判断为非行动事项');
    });

    it('应该识别垃圾信息', async () => {
      context = new DecisionContext({
        ...mockItem,
        description: '删除这个无用的信息'
      });

      const result = await rule.evaluate(context);

      expect(result.decision?.isActionable).toBe(false);
      expect(result.decision?.targetList).toBe('trash');
    });

    it('应该识别将来/也许事项', async () => {
      context = new DecisionContext({
        ...mockItem,
        description: '将来可能要学习的新技能'
      });

      const result = await rule.evaluate(context);

      expect(result.decision?.isActionable).toBe(false);
      expect(result.decision?.targetList).toBe('incubator');
    });
  });

  describe('TimeRule', () => {
    let rule: TimeRule;

    beforeEach(() => {
      rule = new TimeRule();
    });

    it('应该识别快速行动', async () => {
      context = new DecisionContext({
        ...mockItem,
        description: '发送邮件',
        estimatedMinutes: 1
      });
      context.setActionable(true);

      const result = await rule.evaluate(context);

      expect(result.decision?.canDoInTwoMinutes).toBe(true);
      expect(result.decision?.quickActionSuggestion).toBe(true);
      expect(result.reasoning).toContain('符合2分钟规则');
    });

    it('应该识别非快速行动', async () => {
      context = new DecisionContext({
        ...mockItem,
        description: '开发新功能',
        estimatedMinutes: 120
      });
      context.setActionable(true);

      const result = await rule.evaluate(context);

      expect(result.decision?.canDoInTwoMinutes).toBe(false);
      expect(result.reasoning).toContain('超过2分钟阈值');
    });

    it('应该跳过非行动事项', async () => {
      context = new DecisionContext({
        ...mockItem,
        description: '参考资料'
      });
      context.setActionable(false);

      const result = await rule.evaluate(context);

      expect(result.reasoning).toContain('跳过时间规则：非行动事项');
      expect(result.shouldContinue).toBe(true);
    });

    it('应该基于描述估算时间', async () => {
      context = new DecisionContext({
        ...mockItem,
        description: '快速检查邮件'
      });
      context.setActionable(true);

      const result = await rule.evaluate(context);

      expect(result.decision?.canDoInTwoMinutes).toBe(true);
      expect(result.reasoning.some(r => r.includes('预估时间'))).toBe(true);
    });
  });

  describe('OwnershipRule', () => {
    let rule: OwnershipRule;

    beforeEach(() => {
      rule = new OwnershipRule();
    });

    it('应该识别需要委派的任务', async () => {
      context = new DecisionContext({
        ...mockItem,
        description: '让小王帮忙整理文档'
      });
      context.setActionable(true);
      context.setCanDoInTwoMinutes(false);

      const result = await rule.evaluate(context);

      expect(result.decision?.isMyResponsibility).toBe(false);
      expect(result.decision?.targetList).toBe('waiting');
      expect(result.reasoning).toContain('判断为需要委派的任务');
    });

    it('应该识别自己负责的任务', async () => {
      context = new DecisionContext({
        ...mockItem,
        description: '我需要完成这个报告'
      });
      context.setActionable(true);
      context.setCanDoInTwoMinutes(false);

      const result = await rule.evaluate(context);

      expect(result.decision?.isMyResponsibility).toBe(true);
      expect(result.reasoning).toContain('判断为自己负责的任务');
    });

    it('应该默认快速行动为自己执行', async () => {
      context = new DecisionContext({
        ...mockItem,
        description: '发送邮件'
      });
      context.setActionable(true);
      context.setCanDoInTwoMinutes(true);

      const result = await rule.evaluate(context);

      expect(result.decision?.isMyResponsibility).toBe(true);
      expect(result.reasoning).toContain('快速行动默认自己执行');
    });
  });

  describe('ComplexityRule', () => {
    let rule: ComplexityRule;

    beforeEach(() => {
      rule = new ComplexityRule();
    });

    it('应该识别单一行动', async () => {
      context = new DecisionContext({
        ...mockItem,
        description: '打电话给客户',
        estimatedMinutes: 10
      });
      context.setActionable(true);
      context.setMyResponsibility(true);

      const result = await rule.evaluate(context);

      expect(result.decision?.isSingleAction).toBe(true);
      expect(result.decision?.targetList).toBe('next-actions');
      expect(result.reasoning).toContain('判断为单一行动');
    });

    it('应该识别多步骤项目', async () => {
      context = new DecisionContext({
        ...mockItem,
        description: '开发新的客户管理系统',
        estimatedMinutes: 240
      });
      context.setActionable(true);
      context.setMyResponsibility(true);

      const result = await rule.evaluate(context);

      expect(result.decision?.isSingleAction).toBe(false);
      expect(result.decision?.targetList).toBe('projects');
      expect(result.reasoning).toContain('判断为多步骤项目');
    });

    it('应该基于步骤数量判断复杂度', async () => {
      context = new DecisionContext({
        ...mockItem,
        description: '1. 收集需求 2. 设计方案 3. 实施开发'
      });
      context.setActionable(true);
      context.setMyResponsibility(true);

      const result = await rule.evaluate(context);

      expect(result.decision?.isSingleAction).toBe(false);
      expect(result.reasoning.some(r => r.includes('检测到3个步骤'))).toBe(true);
    });
  });

  describe('CalendarRule', () => {
    let rule: CalendarRule;

    beforeEach(() => {
      rule = new CalendarRule();
    });

    it('应该识别需要放入日历的事项', async () => {
      context = new DecisionContext({
        ...mockItem,
        description: '明天下午2点开会讨论项目进展'
      });
      context.setActionable(true);
      context.setMyResponsibility(true);
      context.setSingleAction(true);

      const result = await rule.evaluate(context);

      expect(result.decision?.hasSpecificTime).toBe(true);
      expect(result.decision?.targetList).toBe('calendar');
      expect(result.reasoning).toContain('判断为需要放入日历的事项');
    });

    it('应该识别执行清单事项', async () => {
      context = new DecisionContext({
        ...mockItem,
        description: '完成报告撰写'
      });
      context.setActionable(true);
      context.setMyResponsibility(true);
      context.setSingleAction(true);

      const result = await rule.evaluate(context);

      expect(result.decision?.hasSpecificTime).toBe(false);
      expect(result.reasoning).toContain('判断为执行清单事项');
    });

    it('应该识别重复事件', async () => {
      context = new DecisionContext({
        ...mockItem,
        description: '每周一的团队例会'
      });
      context.setActionable(true);
      context.setMyResponsibility(true);
      context.setSingleAction(true);

      const result = await rule.evaluate(context);

      expect(result.decision?.hasSpecificTime).toBe(true);
      expect(result.reasoning.some(r => r.includes('重复事件'))).toBe(true);
    });

    it('应该跳过非单一行动', async () => {
      context = new DecisionContext({
        ...mockItem,
        description: '会议'
      });
      context.setActionable(true);
      context.setMyResponsibility(true);
      context.setSingleAction(false);

      const result = await rule.evaluate(context);

      expect(result.reasoning).toContain('跳过日历规则：非单一行动或非自己负责');
    });
  });

  describe('规则集成测试', () => {
    it('应该正确处理完整的决策流程', async () => {
      const testCases = [
        {
          description: '打电话给客户确认订单',
          expected: {
            isActionable: true,
            canDoInTwoMinutes: true,
            isMyResponsibility: true,
            targetList: 'next-actions'
          }
        },
        {
          description: '让助理安排下周的会议',
          expected: {
            isActionable: true,
            isMyResponsibility: false,
            targetList: 'waiting'
          }
        },
        {
          description: '开发新的项目管理系统',
          expected: {
            isActionable: true,
            isSingleAction: false,
            targetList: 'projects'
          }
        },
        {
          description: '明天上午10点参加董事会会议',
          expected: {
            isActionable: true,
            hasSpecificTime: true,
            targetList: 'calendar'
          }
        }
      ];

      for (const testCase of testCases) {
        const context = new DecisionContext({
          ...mockItem,
          description: testCase.description
        });

        // 模拟完整的决策流程
        const rules = [
          new ActionabilityRule(),
          new TimeRule(),
          new OwnershipRule(),
          new ComplexityRule(),
          new CalendarRule()
        ];

        for (const rule of rules) {
          await rule.evaluate(context);
          if (context.isDecisionComplete()) break;
        }

        const decision = context.getDecision();

        Object.entries(testCase.expected).forEach(([key, value]) => {
          expect(decision[key as keyof typeof decision]).toBe(value);
        });
      }
    });
  });
});
