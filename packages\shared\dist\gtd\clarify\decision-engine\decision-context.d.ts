import { IDecisionContext, ClarifyDecision, TargetList, UserPreferences, WorkloadInfo, TimeContext } from '../types';
import { InboxItem } from '../../../types';
/**
 * 决策上下文实现类
 * 负责管理整个决策过程中的状态、数据和中间结果
 */
export declare class DecisionContext implements IDecisionContext {
    readonly item: InboxItem;
    readonly userPreferences: UserPreferences;
    readonly currentWorkload?: WorkloadInfo;
    readonly timeContext?: TimeContext;
    private decision;
    private reasoning;
    private appliedRules;
    private isComplete;
    private confidence;
    private estimatedMinutes?;
    private extractedContexts;
    private detectedKeywords;
    constructor(item: InboxItem, userPreferences?: UserPreferences, currentWorkload?: WorkloadInfo, timeContext?: TimeContext);
    /**
     * 设置可行动性
     */
    setActionable(isActionable: boolean, reasoning?: string): void;
    /**
     * 设置2分钟规则结果
     */
    setCanDoInTwoMinutes(canDo: boolean, reasoning?: string): void;
    /**
     * 设置责任归属
     */
    setMyResponsibility(isMine: boolean, reasoning?: string): void;
    /**
     * 设置行动复杂度
     */
    setSingleAction(isSingle: boolean, reasoning?: string): void;
    /**
     * 设置时间特定性
     */
    setHasSpecificTime(hasTime: boolean, reasoning?: string): void;
    /**
     * 设置目标清单
     */
    setTargetList(targetList: TargetList, reasoning?: string): void;
    /**
     * 设置快速行动建议
     */
    setQuickActionSuggestion(suggest: boolean): void;
    /**
     * 添加推理信息
     */
    addReasoning(reason: string): void;
    /**
     * 记录应用的规则
     */
    addAppliedRule(ruleName: string): void;
    /**
     * 更新置信度
     */
    updateConfidence(confidence: number): void;
    /**
     * 标记决策完成
     */
    completeDecision(): void;
    /**
     * 检查决策是否完成
     */
    isDecisionComplete(): boolean;
    /**
     * 获取最终决策结果
     */
    getDecision(): ClarifyDecision;
    /**
     * 获取收集箱条目
     */
    getItem(): InboxItem;
    /**
     * 设置预估时间
     */
    setEstimatedMinutes(minutes: number): void;
    /**
     * 获取预估时间
     */
    getEstimatedMinutes(): number;
    /**
     * 添加检测到的关键词
     */
    addDetectedKeyword(keyword: string): void;
    /**
     * 获取检测到的关键词
     */
    getDetectedKeywords(): string[];
    /**
     * 设置提取的情境
     */
    setExtractedContexts(contexts: string[]): void;
    /**
     * 获取提取的情境
     */
    getExtractedContexts(): string[];
    /**
     * 初始化决策数据
     */
    private initializeDecision;
    /**
     * 检查是否有必需的决策信息
     */
    private hasRequiredDecisions;
    /**
     * 推断目标清单
     */
    private inferTargetList;
}
//# sourceMappingURL=decision-context.d.ts.map