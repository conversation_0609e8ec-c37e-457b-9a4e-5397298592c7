// 数据库相关类型定义

import {
  InboxItem,
  InboxStatus,
  CaptureSource,
  TaskCategory,
  Task,
  TaskStatus,
  CreateTaskInput,
  Project,
  ProjectStatus,
  CreateProjectInput
} from './index';

// 数据库配置
export interface DatabaseConfig {
  path: string;
  enableWAL?: boolean;
  enableForeignKeys?: boolean;
  busyTimeout?: number;
}

// 数据库连接接口
export interface DatabaseConnection {
  run(sql: string, params?: any[]): Promise<{ lastID: number; changes: number }>;
  get<T = any>(sql: string, params?: any[]): Promise<T | undefined>;
  all<T = any>(sql: string, params?: any[]): Promise<T[]>;
  close(): Promise<void>;
  beginTransaction(): Promise<void>;
  commit(): Promise<void>;
  rollback(): Promise<void>;
}

// 收集箱数据库记录 (对应数据库表结构)
export interface InboxItemRecord {
  id: string;
  description: string;
  expected_outcome?: string;
  deadline?: string;           // ISO 8601 字符串
  estimated_minutes?: number;
  category?: TaskCategory;
  source: CaptureSource;
  status: InboxStatus;
  raw_input?: string;
  ai_suggestions?: string;     // JSON 字符串
  created_at: string;         // ISO 8601 字符串
  updated_at: string;         // ISO 8601 字符串
  version: number;
}

// 数据库查询选项
export interface QueryOptions {
  limit?: number;
  offset?: number;
  orderBy?: string;
  orderDirection?: 'ASC' | 'DESC';
}

// 收集箱查询过滤器
export interface InboxQueryFilter {
  status?: InboxStatus;
  source?: CaptureSource;
  category?: TaskCategory;
  createdAfter?: Date;
  createdBefore?: Date;
  hasDeadline?: boolean;
}

// 仓储接口
export interface InboxRepository {
  // 基础 CRUD 操作
  save(item: InboxItem): Promise<string>;           // 返回ID
  findById(id: string): Promise<InboxItem | null>;
  findAll(options?: QueryOptions): Promise<InboxItem[]>;
  update(id: string, updates: Partial<InboxItem>): Promise<InboxItem>;
  delete(id: string): Promise<void>;
  create(item: Partial<InboxItem>): Promise<InboxItem>;
  
  // 查询操作
  findByStatus(status: InboxStatus, options?: QueryOptions): Promise<InboxItem[]>;
  findByFilter(filter: InboxQueryFilter, options?: QueryOptions): Promise<InboxItem[]>;
  count(filter?: InboxQueryFilter): Promise<number>;
  
  // 批量操作
  saveMany(items: InboxItem[]): Promise<string[]>;
  updateMany(updates: Array<{ id: string; data: Partial<InboxItem> }>): Promise<void>;
  deleteMany(ids: string[]): Promise<void>;
  
  // 数据库管理
  initialize(): Promise<void>;
  close(): Promise<void>;
  vacuum(): Promise<void>;
}

// 任务仓储接口
export interface TaskRepository {
  // 基础 CRUD 操作
  findById(id: string): Promise<Task | null>;
  findAll(options?: QueryOptions): Promise<Task[]>;
  create(task: CreateTaskInput): Promise<Task>;
  update(id: string, updates: Partial<Task>): Promise<Task>;
  delete(id: string): Promise<void>;

  // 查询操作
  findByStatus(status: TaskStatus, options?: QueryOptions): Promise<Task[]>;
  findByCategory(category: TaskCategory, options?: QueryOptions): Promise<Task[]>;
  findByProject(projectId: string, options?: QueryOptions): Promise<Task[]>;

  // 统计操作
  count(): Promise<number>;
  countByStatus(): Promise<Record<TaskStatus, number>>;
}

// 项目仓储接口
export interface ProjectRepository {
  // 基础 CRUD 操作
  findById(id: string): Promise<Project | null>;
  findAll(options?: QueryOptions): Promise<Project[]>;
  create(project: CreateProjectInput): Promise<Project>;
  update(id: string, updates: Partial<Project>): Promise<Project>;
  delete(id: string): Promise<void>;

  // 查询操作
  findByStatus(status: ProjectStatus, options?: QueryOptions): Promise<Project[]>;
  findByCategory(category: TaskCategory, options?: QueryOptions): Promise<Project[]>;

  // 统计操作
  count(): Promise<number>;
  countByStatus(): Promise<Record<ProjectStatus, number>>;
}
