import { InboxItem } from '../../../types';
/**
 * 明晰向导步骤类型
 */
export type ClarifyStep = 'actionability' | 'time' | 'ownership' | 'complexity' | 'calendar' | 'complete';
/**
 * 向导会话状态
 */
export interface WizardSession {
    id: string;
    itemId: string;
    currentStep: ClarifyStep;
    answers: WizardAnswers;
    isComplete: boolean;
    createdAt: Date;
    updatedAt: Date;
}
/**
 * 用户的答案记录
 */
export interface WizardAnswers {
    isActionable?: boolean;
    canDoInTwoMinutes?: boolean;
    isMyResponsibility?: boolean;
    isSingleAction?: boolean;
    hasSpecificTime?: boolean;
    delegatedTo?: string;
    expectedDate?: Date;
    projectOutcome?: string;
    calendarTime?: Date;
    notes?: string;
}
/**
 * 步骤问题定义
 */
export interface StepQuestion {
    step: ClarifyStep;
    title: string;
    description: string;
    options: StepOption[];
    helpText?: string;
    examples?: string[];
}
/**
 * 选项定义
 */
export interface StepOption {
    value: any;
    label: string;
    description?: string;
    nextStep?: ClarifyStep;
    requiresInput?: boolean;
    inputType?: 'text' | 'date' | 'person';
    inputLabel?: string;
}
/**
 * GTD 明晰向导类
 * 提供用户主导的逐步决策流程
 */
export declare class ClarifyWizard {
    private sessions;
    /**
     * 开始明晰流程
     */
    startClarification(item: InboxItem): Promise<WizardSession>;
    /**
     * 获取当前步骤的问题
     */
    getCurrentQuestion(sessionId: string): StepQuestion | null;
    /**
     * 提交答案并进入下一步
     */
    submitAnswer(sessionId: string, answer: any, additionalData?: Record<string, any>): Promise<WizardSession>;
    /**
     * 获取明晰结果
     */
    getClarificationResult(sessionId: string): ClarificationResult | null;
    /**
     * 获取步骤问题定义
     */
    private getQuestionForStep;
    /**
     * 保存用户答案
     */
    private saveAnswer;
    /**
     * 确定下一步
     */
    private determineNextStep;
    /**
     * 构建最终结果
     */
    private buildResult;
    /**
     * 生成决策摘要
     */
    private generateSummary;
    /**
     * 生成会话ID
     */
    private generateSessionId;
    /**
     * 清理过期会话
     */
    cleanupExpiredSessions(maxAgeHours?: number): void;
}
/**
 * 明晰结果
 */
export interface ClarificationResult {
    sessionId: string;
    targetList: string;
    answers: WizardAnswers;
    summary: string;
}
//# sourceMappingURL=clarify-wizard.d.ts.map