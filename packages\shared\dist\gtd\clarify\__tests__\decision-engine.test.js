"use strict";
// 决策引擎单元测试
Object.defineProperty(exports, "__esModule", { value: true });
const decision_engine_1 = require("../decision-engine");
const actionability_rule_1 = require("../decision-engine/rules/actionability-rule");
const time_rule_1 = require("../decision-engine/rules/time-rule");
describe('DecisionEngine', () => {
    let decisionTree;
    let mockLogger;
    beforeEach(() => {
        mockLogger = jest.fn();
        decisionTree = new decision_engine_1.DecisionTree(mockLogger);
    });
    describe('DecisionTree', () => {
        it('应该能够添加和管理规则', () => {
            const rule1 = new actionability_rule_1.ActionabilityRule();
            const rule2 = new time_rule_1.TimeRule();
            decisionTree.addRule(rule1);
            decisionTree.addRule(rule2);
            const rules = decisionTree.getRules();
            expect(rules).toHaveLength(2);
            expect(rules[0]).toBeInstanceOf(actionability_rule_1.ActionabilityRule);
            expect(rules[1]).toBeInstanceOf(time_rule_1.TimeRule);
        });
        it('应该按优先级排序规则', () => {
            const rule1 = new time_rule_1.TimeRule(); // priority: 2
            const rule2 = new actionability_rule_1.ActionabilityRule(); // priority: 1
            decisionTree.addRule(rule1);
            decisionTree.addRule(rule2);
            const rules = decisionTree.getRules();
            expect(rules[0]).toBeInstanceOf(actionability_rule_1.ActionabilityRule); // 优先级1，应该排在前面
            expect(rules[1]).toBeInstanceOf(time_rule_1.TimeRule); // 优先级2，应该排在后面
        });
        it('应该能够移除规则', () => {
            const rule = new actionability_rule_1.ActionabilityRule();
            decisionTree.addRule(rule);
            expect(decisionTree.getRules()).toHaveLength(1);
            const removed = decisionTree.removeRule('ActionabilityRule');
            expect(removed).toBe(true);
            expect(decisionTree.getRules()).toHaveLength(0);
        });
        it('应该能够处理单个收集箱条目', async () => {
            const mockItem = {
                id: 'test-1',
                description: '打电话给客户确认订单',
                source: 'manual',
                status: 'captured',
                createdAt: new Date(),
                updatedAt: new Date(),
                version: 1
            };
            decisionTree.addRule(new actionability_rule_1.ActionabilityRule());
            decisionTree.addRule(new time_rule_1.TimeRule());
            const decision = await decisionTree.processItem(mockItem);
            expect(decision).toBeDefined();
            expect(decision.isActionable).toBe(true);
            expect(decision.targetList).toBeDefined();
            expect(decision.reasoning).toBeInstanceOf(Array);
            expect(decision.confidence).toBeGreaterThan(0);
        });
        it('应该处理非行动事项', async () => {
            const mockItem = {
                id: 'test-2',
                description: '参考资料：项目管理最佳实践',
                source: 'manual',
                status: 'captured',
                createdAt: new Date(),
                updatedAt: new Date(),
                version: 1
            };
            decisionTree.addRule(new actionability_rule_1.ActionabilityRule());
            const decision = await decisionTree.processItem(mockItem);
            expect(decision.isActionable).toBe(false);
            expect(['trash', 'reference', 'incubator']).toContain(decision.targetList);
        });
        it('应该处理快速行动', async () => {
            const mockItem = {
                id: 'test-3',
                description: '发送邮件给同事',
                estimatedMinutes: 1,
                source: 'manual',
                status: 'captured',
                createdAt: new Date(),
                updatedAt: new Date(),
                version: 1
            };
            decisionTree.addRule(new actionability_rule_1.ActionabilityRule());
            decisionTree.addRule(new time_rule_1.TimeRule());
            const decision = await decisionTree.processItem(mockItem);
            expect(decision.isActionable).toBe(true);
            expect(decision.canDoInTwoMinutes).toBe(true);
            expect(decision.quickActionSuggestion).toBe(true);
        });
        it('应该处理批量条目', async () => {
            const mockItems = [
                {
                    id: 'test-4',
                    description: '打电话给客户',
                    source: 'manual',
                    status: 'captured',
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    version: 1
                },
                {
                    id: 'test-5',
                    description: '参考资料文档',
                    source: 'manual',
                    status: 'captured',
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    version: 1
                }
            ];
            decisionTree.addRule(new actionability_rule_1.ActionabilityRule());
            const decisions = await decisionTree.processItems(mockItems);
            expect(decisions).toHaveLength(2);
            expect(decisions[0].isActionable).toBe(true);
            expect(decisions[1].isActionable).toBe(false);
        });
        it('应该处理规则执行错误', async () => {
            const mockItem = {
                id: 'test-6',
                description: '测试错误处理',
                source: 'manual',
                status: 'captured',
                createdAt: new Date(),
                updatedAt: new Date(),
                version: 1
            };
            // 创建一个会抛出错误的模拟规则
            const errorRule = {
                name: 'ErrorRule',
                priority: 1,
                evaluate: jest.fn().mockRejectedValue(new Error('测试错误'))
            };
            decisionTree.addRule(errorRule);
            decisionTree.addRule(new actionability_rule_1.ActionabilityRule());
            const decision = await decisionTree.processItem(mockItem);
            // 应该继续执行其他规则，不会因为一个规则错误而中断
            expect(decision).toBeDefined();
            expect(decision.reasoning).toContain('规则 ErrorRule 执行失败: 测试错误');
        });
    });
    describe('DecisionEngineFactory', () => {
        it('应该创建标准决策引擎', () => {
            const engine = decision_engine_1.DecisionEngineFactory.createStandardEngine();
            const rules = engine.getRules();
            expect(rules).toHaveLength(5); // 5个核心规则
            expect(rules.map(r => r.name)).toEqual([
                'ActionabilityRule',
                'TimeRule',
                'OwnershipRule',
                'ComplexityRule',
                'CalendarRule'
            ]);
        });
        it('应该创建最小化决策引擎', () => {
            const engine = decision_engine_1.DecisionEngineFactory.createMinimalEngine();
            const rules = engine.getRules();
            expect(rules).toHaveLength(2); // 只有2个基本规则
            expect(rules.map(r => r.name)).toEqual([
                'ActionabilityRule',
                'TimeRule'
            ]);
        });
        it('应该创建自定义决策引擎', () => {
            const customRules = [actionability_rule_1.ActionabilityRule, time_rule_1.TimeRule];
            const engine = decision_engine_1.DecisionEngineFactory.createCustomEngine(customRules);
            const rules = engine.getRules();
            expect(rules).toHaveLength(2);
            expect(rules[0]).toBeInstanceOf(actionability_rule_1.ActionabilityRule);
            expect(rules[1]).toBeInstanceOf(time_rule_1.TimeRule);
        });
        it('应该提供规则信息', () => {
            const ruleInfo = decision_engine_1.DecisionEngineFactory.getRuleInfo();
            expect(ruleInfo).toHaveLength(5);
            expect(ruleInfo[0]).toEqual({
                name: 'ActionabilityRule',
                priority: 1,
                description: '判断事项是否可行动，区分行动项目和非行动项目'
            });
        });
        it('应该提供可用规则列表', () => {
            const availableRules = decision_engine_1.DecisionEngineFactory.getAvailableRules();
            expect(availableRules).toHaveLength(5);
            expect(availableRules[0]).toBe(actionability_rule_1.ActionabilityRule);
        });
    });
    describe('错误处理', () => {
        it('应该处理无效的收集箱条目', async () => {
            const invalidItem = null;
            await expect(decisionTree.processItem(invalidItem)).rejects.toThrow();
        });
        it('应该处理置信度过低的情况', async () => {
            const mockItem = {
                id: 'test-7',
                description: '', // 空描述可能导致低置信度
                source: 'manual',
                status: 'captured',
                createdAt: new Date(),
                updatedAt: new Date(),
                version: 1
            };
            decisionTree.addRule(new actionability_rule_1.ActionabilityRule());
            // 根据实现，可能会抛出置信度不足的错误，或者返回低置信度的决策
            try {
                const decision = await decisionTree.processItem(mockItem);
                expect(decision.confidence).toBeDefined();
            }
            catch (error) {
                expect(error).toBeInstanceOf(Error);
            }
        });
    });
    describe('日志记录', () => {
        it('应该记录决策过程', async () => {
            const mockItem = {
                id: 'test-8',
                description: '打电话给客户',
                source: 'manual',
                status: 'captured',
                createdAt: new Date(),
                updatedAt: new Date(),
                version: 1
            };
            decisionTree.addRule(new actionability_rule_1.ActionabilityRule());
            await decisionTree.processItem(mockItem);
            expect(mockLogger).toHaveBeenCalled();
            expect(mockLogger).toHaveBeenCalledWith('info', '开始处理收集箱条目', expect.objectContaining({
                itemId: 'test-8',
                description: '打电话给客户'
            }));
        });
    });
});
//# sourceMappingURL=decision-engine.test.js.map