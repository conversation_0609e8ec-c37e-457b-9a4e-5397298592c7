"use strict";
// 决策引擎模块导出
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DecisionEngineFactory = void 0;
__exportStar(require("./decision-context"), exports);
__exportStar(require("./decision-tree"), exports);
// 规则导出
__exportStar(require("./rules/actionability-rule"), exports);
__exportStar(require("./rules/time-rule"), exports);
__exportStar(require("./rules/ownership-rule"), exports);
__exportStar(require("./rules/complexity-rule"), exports);
__exportStar(require("./rules/calendar-rule"), exports);
const decision_tree_1 = require("./decision-tree");
const actionability_rule_1 = require("./rules/actionability-rule");
const time_rule_1 = require("./rules/time-rule");
const ownership_rule_1 = require("./rules/ownership-rule");
const complexity_rule_1 = require("./rules/complexity-rule");
const calendar_rule_1 = require("./rules/calendar-rule");
/**
 * 决策引擎工厂类
 * 提供便捷的决策引擎创建和配置方法
 */
class DecisionEngineFactory {
    /**
     * 创建标准的GTD决策引擎
     * 包含所有核心决策规则
     */
    static createStandardEngine(logger) {
        const engine = new decision_tree_1.DecisionTree(logger);
        // 添加核心决策规则（按GTD流程顺序）
        engine.addRules([
            new actionability_rule_1.ActionabilityRule(), // 1. 可行动性检查
            new time_rule_1.TimeRule(), // 2. 时间判断 (2分钟规则)
            new ownership_rule_1.OwnershipRule(), // 3. 责任归属判断
            new complexity_rule_1.ComplexityRule(), // 4. 复杂度判断 (单一行动 vs 项目)
            new calendar_rule_1.CalendarRule() // 5. 日历判断
        ]);
        return engine;
    }
    /**
     * 创建自定义决策引擎
     * 允许指定特定的规则集合
     */
    static createCustomEngine(rules, logger) {
        const engine = new decision_tree_1.DecisionTree(logger);
        // 实例化并添加规则
        const ruleInstances = rules.map(RuleClass => new RuleClass());
        engine.addRules(ruleInstances);
        return engine;
    }
    /**
     * 创建最小化决策引擎
     * 只包含最基本的决策规则
     */
    static createMinimalEngine(logger) {
        const engine = new decision_tree_1.DecisionTree(logger);
        // 只添加最基本的规则
        engine.addRules([
            new actionability_rule_1.ActionabilityRule(),
            new time_rule_1.TimeRule()
        ]);
        return engine;
    }
    /**
     * 获取所有可用的规则类
     */
    static getAvailableRules() {
        return [
            actionability_rule_1.ActionabilityRule,
            time_rule_1.TimeRule,
            ownership_rule_1.OwnershipRule,
            complexity_rule_1.ComplexityRule,
            calendar_rule_1.CalendarRule
        ];
    }
    /**
     * 获取规则信息
     */
    static getRuleInfo() {
        return [
            {
                name: 'ActionabilityRule',
                priority: 1,
                description: '判断事项是否可行动，区分行动项目和非行动项目'
            },
            {
                name: 'TimeRule',
                priority: 2,
                description: '应用2分钟规则，识别快速行动'
            },
            {
                name: 'OwnershipRule',
                priority: 3,
                description: '判断责任归属，识别需要委派的事项'
            },
            {
                name: 'ComplexityRule',
                priority: 4,
                description: '判断复杂度，区分单一行动和多步骤项目'
            },
            {
                name: 'CalendarRule',
                priority: 5,
                description: '识别有特定时间要求的事项'
            }
        ];
    }
}
exports.DecisionEngineFactory = DecisionEngineFactory;
//# sourceMappingURL=index.js.map