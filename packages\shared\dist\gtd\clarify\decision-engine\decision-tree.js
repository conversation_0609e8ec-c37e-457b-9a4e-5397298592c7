"use strict";
// 决策树类 - GTD明晰流程的核心决策引擎
Object.defineProperty(exports, "__esModule", { value: true });
exports.DecisionTree = void 0;
const decision_context_1 = require("./decision-context");
const constants_1 = require("../constants");
/**
 * 决策树类
 * 负责协调和执行整个GTD明晰决策流程
 */
class DecisionTree {
    rules = [];
    logger;
    constructor(logger) {
        this.logger = logger;
    }
    // === 规则管理 ===
    /**
     * 添加决策规则
     */
    addRule(rule) {
        this.rules.push(rule);
        // 按优先级排序
        this.rules.sort((a, b) => a.priority - b.priority);
        this.log('debug', `添加决策规则: ${rule.name}`, { priority: rule.priority });
    }
    /**
     * 批量添加规则
     */
    addRules(rules) {
        rules.forEach(rule => this.addRule(rule));
    }
    /**
     * 移除规则
     */
    removeRule(ruleName) {
        const index = this.rules.findIndex(rule => rule.name === ruleName);
        if (index !== -1) {
            this.rules.splice(index, 1);
            this.log('debug', `移除决策规则: ${ruleName}`);
            return true;
        }
        return false;
    }
    /**
     * 获取所有规则
     */
    getRules() {
        return [...this.rules];
    }
    // === 决策处理 ===
    /**
     * 处理单个收集箱条目
     */
    async processItem(item, context) {
        const startTime = Date.now();
        try {
            this.log('info', '开始处理收集箱条目', {
                itemId: item.id,
                description: item.description
            });
            // 创建决策上下文
            const decisionContext = new decision_context_1.DecisionContext(item, context?.userPreferences, context?.currentWorkload, context?.timeContext);
            // 执行决策规则
            await this.executeRules(decisionContext);
            // 获取最终决策
            const decision = decisionContext.getDecision();
            // 验证决策结果
            this.validateDecision(decision);
            const processingTime = Date.now() - startTime;
            this.log('info', '决策处理完成', {
                itemId: item.id,
                targetList: decision.targetList,
                confidence: decision.confidence,
                processingTime
            });
            return decision;
        }
        catch (error) {
            const processingTime = Date.now() - startTime;
            this.log('error', '决策处理失败', {
                itemId: item.id,
                error: error instanceof Error ? error.message : String(error),
                processingTime
            });
            throw error;
        }
    }
    /**
     * 批量处理收集箱条目
     */
    async processItems(items, context) {
        const results = [];
        const errors = [];
        this.log('info', '开始批量处理', { itemCount: items.length });
        for (const item of items) {
            try {
                const decision = await this.processItem(item, context);
                results.push(decision);
            }
            catch (error) {
                const errorMessage = error instanceof Error ? error.message : String(error);
                errors.push({ itemId: item.id, error: errorMessage });
                this.log('error', '批量处理中的单项错误', {
                    itemId: item.id,
                    error: errorMessage
                });
            }
        }
        this.log('info', '批量处理完成', {
            total: items.length,
            successful: results.length,
            failed: errors.length
        });
        return results;
    }
    // === 私有方法 ===
    /**
     * 执行所有决策规则
     */
    async executeRules(context) {
        this.log('debug', '开始执行决策规则', {
            ruleCount: this.rules.length,
            itemId: context.getItem().id
        });
        for (const rule of this.rules) {
            try {
                // 检查是否已经完成决策
                if (context.isDecisionComplete()) {
                    this.log('debug', '决策已完成，跳过后续规则', {
                        completedAt: rule.name
                    });
                    break;
                }
                this.log('debug', `执行规则: ${rule.name}`, {
                    priority: rule.priority
                });
                // 执行规则
                const ruleResult = await rule.evaluate(context);
                // 处理规则结果
                this.processRuleResult(context, ruleResult);
                // 记录规则应用
                context.addAppliedRule(rule.name);
                this.log('debug', `规则执行完成: ${rule.name}`, {
                    confidence: ruleResult.confidence,
                    shouldContinue: ruleResult.shouldContinue
                });
                // 检查是否应该继续执行后续规则
                if (!ruleResult.shouldContinue) {
                    this.log('debug', '规则要求停止执行后续规则', {
                        stoppedAt: rule.name
                    });
                    break;
                }
            }
            catch (error) {
                const errorMessage = error instanceof Error ? error.message : String(error);
                this.log('error', `规则执行错误: ${rule.name}`, {
                    error: errorMessage
                });
                // 规则执行错误不应该中断整个决策流程
                context.addReasoning(`规则 ${rule.name} 执行失败: ${errorMessage}`);
            }
        }
    }
    /**
     * 处理规则执行结果
     */
    processRuleResult(context, result) {
        // 合并决策信息
        if (result.decision) {
            const decision = result.decision;
            if (decision.isActionable !== undefined) {
                context.setActionable(decision.isActionable);
            }
            if (decision.canDoInTwoMinutes !== undefined) {
                context.setCanDoInTwoMinutes(decision.canDoInTwoMinutes);
            }
            if (decision.isMyResponsibility !== undefined) {
                context.setMyResponsibility(decision.isMyResponsibility);
            }
            if (decision.isSingleAction !== undefined) {
                context.setSingleAction(decision.isSingleAction);
            }
            if (decision.hasSpecificTime !== undefined) {
                context.setHasSpecificTime(decision.hasSpecificTime);
            }
            if (decision.targetList) {
                context.setTargetList(decision.targetList);
            }
            if (decision.quickActionSuggestion !== undefined) {
                context.setQuickActionSuggestion(decision.quickActionSuggestion);
            }
        }
        // 添加推理信息
        result.reasoning.forEach(reason => context.addReasoning(reason));
        // 更新置信度
        context.updateConfidence(result.confidence);
    }
    /**
     * 验证决策结果
     */
    validateDecision(decision) {
        // 检查必需字段
        if (decision.isActionable === undefined) {
            throw new Error(`${constants_1.ERROR_MESSAGES.DECISION_ENGINE_ERROR}: 缺少可行动性判断`);
        }
        if (!decision.targetList) {
            throw new Error(`${constants_1.ERROR_MESSAGES.DECISION_ENGINE_ERROR}: 缺少目标清单`);
        }
        // 检查置信度
        if (decision.confidence < constants_1.CONFIDENCE_THRESHOLDS.MINIMUM) {
            throw new Error(`${constants_1.ERROR_MESSAGES.INSUFFICIENT_CONFIDENCE}: ${decision.confidence}`);
        }
        // 检查决策逻辑一致性
        this.validateDecisionLogic(decision);
    }
    /**
     * 验证决策逻辑一致性
     */
    validateDecisionLogic(decision) {
        // 非行动项目不应该有行动相关的属性
        if (!decision.isActionable) {
            if (decision.canDoInTwoMinutes !== undefined ||
                decision.isMyResponsibility !== undefined ||
                decision.isSingleAction !== undefined ||
                decision.hasSpecificTime !== undefined) {
                this.log('warn', '非行动项目包含行动相关属性', { decision });
            }
        }
        // 2分钟规则的逻辑检查
        if (decision.canDoInTwoMinutes && decision.targetList === 'projects') {
            this.log('warn', '2分钟内可完成的任务被分配到项目清单', { decision });
        }
        // 委派逻辑检查
        if (decision.isMyResponsibility === false && decision.targetList !== 'waiting') {
            this.log('warn', '非我负责的任务未分配到等待清单', { decision });
        }
    }
    /**
     * 日志记录
     */
    log(level, message, data) {
        if (this.logger) {
            this.logger(level, message, data);
        }
    }
}
exports.DecisionTree = DecisionTree;
//# sourceMappingURL=decision-tree.js.map