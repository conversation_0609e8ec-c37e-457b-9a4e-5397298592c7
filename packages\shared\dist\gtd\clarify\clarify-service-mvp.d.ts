import { InboxItem } from '../../types';
import { InboxRepository, TaskRepository, ProjectRepository } from '../../types/database';
import { WizardSession } from './wizard/clarify-wizard';
/**
 * MVP版本的明晰服务
 * 提供用户主导的逐步决策流程
 */
export declare class ClarifyServiceMVP {
    private inboxRepository;
    private taskRepository;
    private projectRepository;
    private logger?;
    private wizard;
    constructor(inboxRepository: InboxRepository, taskRepository: TaskRepository, projectRepository: ProjectRepository, logger?: ((level: string, message: string, data?: any) => void) | undefined);
    /**
     * 开始明晰流程
     */
    startClarification(itemId: string): Promise<WizardSession>;
    /**
     * 获取当前步骤问题
     */
    getCurrentQuestion(sessionId: string): import("./wizard/clarify-wizard").StepQuestion | null;
    /**
     * 提交答案
     */
    submitAnswer(sessionId: string, answer: any, additionalData?: Record<string, any>): Promise<WizardSession>;
    /**
     * 完成明晰并创建目标条目
     */
    completeClarification(sessionId: string): Promise<ClarifyResultMVP>;
    /**
     * 根据用户决策创建目标条目
     */
    private createTargetItems;
    /**
     * 创建快速行动任务
     */
    private createQuickActionTask;
    /**
     * 创建普通任务
     */
    private createTask;
    /**
     * 创建项目
     */
    private createProject;
    /**
     * 创建等待条目
     */
    private createWaitingItem;
    /**
     * 创建日历条目
     */
    private createCalendarItem;
    /**
     * 创建参考资料
     */
    private createReferenceItem;
    /**
     * 从描述中提取情境（简化版）
     */
    private extractContexts;
    /**
     * 日志记录
     */
    private log;
}
/**
 * MVP版本的明晰结果
 */
export interface ClarifyResultMVP {
    sessionId: string;
    targetList: string;
    summary: string;
    answers: any;
    createdItems: CreatedItem[];
    updatedInboxItem: InboxItem;
}
/**
 * 创建的条目
 */
export interface CreatedItem {
    type: 'task' | 'project' | 'waiting' | 'reference' | 'calendar';
    data: any;
    targetList: string;
    id: string;
}
//# sourceMappingURL=clarify-service-mvp.d.ts.map