import { DecisionRule, RuleResult } from '../../types';
import { DecisionContext } from '../decision-context';
/**
 * 日历规则
 * 判断任务是否有特定时间要求，需要放入日历
 * 只有在特定日期/时间必须执行的事项才放入日历
 */
export declare class CalendarRule implements DecisionRule {
    readonly name = "CalendarRule";
    readonly priority: 5;
    evaluate(context: DecisionContext): Promise<RuleResult>;
    /**
     * 分析时间特定性
     */
    private analyzeTimeSpecificity;
    /**
     * 检查是否为重复事件
     */
    private checkRecurring;
    /**
     * 判断是否需要放入日历
     */
    private determineCalendarNeed;
    /**
     * 检查是否为时间敏感任务
     */
    private isTimeSensitive;
    /**
     * 构建推理过程
     */
    private buildReasoning;
    /**
     * 计算置信度
     */
    private calculateConfidence;
}
//# sourceMappingURL=calendar-rule.d.ts.map