// 可行动性判断规则 - GTD明晰流程的第一步

import { DecisionRule, RuleResult, IDecisionContext } from '../../types';
import { DecisionContext } from '../decision-context';
import { 
  RULE_PRIORITIES, 
  ACTIONABILITY_KEYWORDS, 
  REFERENCE_KEYWORDS,
  CONFIDENCE_THRESHOLDS 
} from '../../constants';

/**
 * 可行动性规则
 * 判断收集箱条目是否为可行动的事项
 * 这是GTD明晰流程的第一个关键决策点
 */
export class ActionabilityRule implements DecisionRule {
  public readonly name = 'ActionabilityRule';
  public readonly priority = RULE_PRIORITIES.ACTIONABILITY;

  async evaluate(context: DecisionContext): Promise<RuleResult> {
    const item = context.getItem();
    const description = item.description.toLowerCase();
    
    // 分析文本内容
    const analysis = this.analyzeText(description);
    
    // 判断可行动性
    const isActionable = this.determineActionability(analysis, item);
    
    // 如果不可行动，进一步分类
    let targetList: string | undefined;
    if (!isActionable) {
      targetList = this.categorizeNonActionable(analysis, description);
    }
    
    // 构建推理过程
    const reasoning = this.buildReasoning(analysis, isActionable, targetList);
    
    // 计算置信度
    const confidence = this.calculateConfidence(analysis);
    
    return {
      ruleName: this.name,
      decision: {
        isActionable,
        targetList: targetList as any
      },
      confidence,
      reasoning,
      shouldContinue: isActionable // 如果不可行动，可以直接结束决策
    };
  }

  /**
   * 分析文本内容
   */
  private analyzeText(description: string): TextAnalysis {
    const analysis: TextAnalysis = {
      hasActionVerbs: false,
      hasVagueVerbs: false,
      hasProjectVerbs: false,
      hasNonActionWords: false,
      hasReferenceIndicators: false,
      actionVerbCount: 0,
      vagueVerbCount: 0,
      projectVerbCount: 0,
      detectedKeywords: []
    };

    // 检查行动动词
    for (const verb of ACTIONABILITY_KEYWORDS.ACTION_VERBS) {
      if (description.includes(verb)) {
        analysis.hasActionVerbs = true;
        analysis.actionVerbCount++;
        analysis.detectedKeywords.push(verb);
      }
    }

    // 检查模糊动词
    for (const verb of ACTIONABILITY_KEYWORDS.VAGUE_VERBS) {
      if (description.includes(verb)) {
        analysis.hasVagueVerbs = true;
        analysis.vagueVerbCount++;
        analysis.detectedKeywords.push(verb);
      }
    }

    // 检查项目动词
    for (const verb of ACTIONABILITY_KEYWORDS.PROJECT_VERBS) {
      if (description.includes(verb)) {
        analysis.hasProjectVerbs = true;
        analysis.projectVerbCount++;
        analysis.detectedKeywords.push(verb);
      }
    }

    // 检查非行动词汇
    for (const word of ACTIONABILITY_KEYWORDS.NON_ACTION_WORDS) {
      if (description.includes(word)) {
        analysis.hasNonActionWords = true;
        analysis.detectedKeywords.push(word);
      }
    }

    // 检查参考资料指示词
    for (const indicator of REFERENCE_KEYWORDS.REFERENCE_INDICATORS) {
      if (description.includes(indicator)) {
        analysis.hasReferenceIndicators = true;
        analysis.detectedKeywords.push(indicator);
      }
    }

    return analysis;
  }

  /**
   * 判断可行动性
   */
  private determineActionability(analysis: TextAnalysis, item: any): boolean {
    // 强行动指示器
    if (analysis.hasActionVerbs && analysis.actionVerbCount >= 1) {
      return true;
    }

    // 强非行动指示器
    if (analysis.hasNonActionWords || analysis.hasReferenceIndicators) {
      return false;
    }

    // 只有模糊动词，可能不可行动
    if (analysis.hasVagueVerbs && !analysis.hasActionVerbs) {
      return false;
    }

    // 检查是否有预期成果（通常表示可行动）
    if (item.expectedOutcome && item.expectedOutcome.trim().length > 0) {
      return true;
    }

    // 检查是否有截止日期（通常表示可行动）
    if (item.deadline) {
      return true;
    }

    // 默认情况下，倾向于认为是可行动的
    // 这符合GTD的原则：当不确定时，倾向于行动
    return true;
  }

  /**
   * 对非行动项目进行分类
   */
  private categorizeNonActionable(analysis: TextAnalysis, description: string): string {
    // 参考资料
    if (analysis.hasReferenceIndicators) {
      return 'reference';
    }

    // 检查是否为垃圾信息
    if (this.isTrash(description)) {
      return 'trash';
    }

    // 检查是否为将来/也许
    if (this.isSomedayMaybe(description, analysis)) {
      return 'incubator';
    }

    // 默认放入孵化池
    return 'incubator';
  }

  /**
   * 判断是否为垃圾信息
   */
  private isTrash(description: string): boolean {
    const trashIndicators = [
      '删除', '取消', '不要', '无用', '垃圾', '废弃',
      '过期', '无效', '错误', '重复'
    ];

    return trashIndicators.some(indicator => description.includes(indicator));
  }

  /**
   * 判断是否为将来/也许
   */
  private isSomedayMaybe(description: string, analysis: TextAnalysis): boolean {
    const somedayIndicators = [
      '将来', '也许', '可能', '考虑', '想要', '希望',
      '有空', '有时间', '以后', '未来', '某天'
    ];

    // 有项目动词但没有明确行动，可能是将来的项目
    if (analysis.hasProjectVerbs && !analysis.hasActionVerbs) {
      return true;
    }

    return somedayIndicators.some(indicator => description.includes(indicator));
  }

  /**
   * 构建推理过程
   */
  private buildReasoning(
    analysis: TextAnalysis, 
    isActionable: boolean, 
    targetList?: string
  ): string[] {
    const reasoning: string[] = [];

    if (isActionable) {
      reasoning.push('判断为可行动事项');
      
      if (analysis.hasActionVerbs) {
        reasoning.push(`检测到行动动词: ${analysis.detectedKeywords.filter(k => 
          ACTIONABILITY_KEYWORDS.ACTION_VERBS.includes(k)
        ).join(', ')}`);
      }
    } else {
      reasoning.push('判断为非行动事项');
      
      if (analysis.hasNonActionWords) {
        reasoning.push('检测到非行动关键词');
      }
      
      if (analysis.hasReferenceIndicators) {
        reasoning.push('检测到参考资料指示词');
      }
      
      if (analysis.hasVagueVerbs && !analysis.hasActionVerbs) {
        reasoning.push('只有模糊动词，缺乏明确行动');
      }
      
      if (targetList) {
        reasoning.push(`分类为: ${this.getTargetListName(targetList)}`);
      }
    }

    return reasoning;
  }

  /**
   * 计算置信度
   */
  private calculateConfidence(analysis: TextAnalysis): number {
    let confidence = CONFIDENCE_THRESHOLDS.MEDIUM;

    // 有明确行动动词，提高置信度
    if (analysis.hasActionVerbs && analysis.actionVerbCount >= 2) {
      confidence += 0.2;
    }

    // 有非行动指示器，提高置信度
    if (analysis.hasNonActionWords || analysis.hasReferenceIndicators) {
      confidence += 0.2;
    }

    // 只有模糊动词，降低置信度
    if (analysis.hasVagueVerbs && !analysis.hasActionVerbs) {
      confidence -= 0.1;
    }

    return Math.min(1.0, Math.max(CONFIDENCE_THRESHOLDS.LOW, confidence));
  }

  /**
   * 获取目标清单名称
   */
  private getTargetListName(targetList: string): string {
    const names: Record<string, string> = {
      'trash': '垃圾箱',
      'reference': '知识库',
      'incubator': '孵化池'
    };
    return names[targetList] || targetList;
  }
}

/**
 * 文本分析结果接口
 */
interface TextAnalysis {
  hasActionVerbs: boolean;
  hasVagueVerbs: boolean;
  hasProjectVerbs: boolean;
  hasNonActionWords: boolean;
  hasReferenceIndicators: boolean;
  actionVerbCount: number;
  vagueVerbCount: number;
  projectVerbCount: number;
  detectedKeywords: string[];
}
